#!/usr/bin/env python3
"""测试修复后的SearchableComboBox

此脚本测试重新实现的SearchableComboBox组件。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import Qt
from presentation.gui.searchable_combobox import SearchableComboBox


class TestFixedComboBoxWindow(QMainWindow):
    """测试修复后的ComboBox窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_test_data()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("修复后的SearchableComboBox测试")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("修复后的SearchableComboBox测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 修复说明
        info_label = QLabel(
            "🔧 修复内容:\n"
            "• 移除了复杂的代理模型实现\n"
            "• 使用简单的项目筛选和更新机制\n"
            "• 保持原生ComboBox的下拉功能\n"
            "• 支持拼音搜索和实时筛选"
        )
        info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # SearchableComboBox测试
        layout.addWidget(QLabel("修复后的SearchableComboBox:"))
        self.cv_combo = SearchableComboBox()
        layout.addWidget(self.cv_combo)
        
        # 当前选择显示
        self.selection_label = QLabel("当前选择: 无")
        self.selection_label.setStyleSheet("margin: 10px; padding: 5px; background: #e8f4fd; border-radius: 3px;")
        layout.addWidget(self.selection_label)
        
        # 测试按钮
        test_button = QPushButton("重新加载测试数据")
        test_button.clicked.connect(self.load_test_data)
        layout.addWidget(test_button)
        
        # 清空搜索按钮
        clear_button = QPushButton("清空搜索")
        clear_button.clicked.connect(self.clear_search)
        layout.addWidget(clear_button)
        
        # 获取选择按钮
        get_button = QPushButton("获取当前选择")
        get_button.clicked.connect(self.get_current_selection)
        layout.addWidget(get_button)
        
        layout.addStretch()
        
        # 连接信号
        self.cv_combo.currentTextChanged.connect(self.on_selection_changed)
    
    def load_test_data(self):
        """加载测试数据"""
        print("\n" + "="*50)
        print("🧪 加载测试数据")
        print("="*50)
        
        # 清空现有数据
        self.cv_combo.clear()
        
        # 模拟CV数据
        test_cvs = [
            {"id": "cv_001", "name": "张三"},
            {"id": "cv_002", "name": "李明轩"},
            {"id": "cv_003", "name": "王诗涵"},
            {"id": "cv_004", "name": "陈奕迅"},
            {"id": "cv_005", "name": "小红"},
            {"id": "cv_006", "name": "赵钱孙"},
            {"id": "cv_007", "name": "周杰伦"},
            {"id": "cv_008", "name": "刘德华"},
            {"id": "cv_009", "name": "郭富城"},
            {"id": "cv_010", "name": "张学友"},
        ]
        
        print(f"🔄 开始加载CV数据，数量: {len(test_cvs)}")
        
        # 添加默认选项
        self.cv_combo.addItem("请选择CV...")
        
        # 添加CV数据
        for i, cv in enumerate(test_cvs):
            cv_name = cv["name"]
            cv_id = cv["id"]
            
            print(f"   添加CV {i+1}: '{cv_name}' (ID: {cv_id})")
            self.cv_combo.addItem(cv_name, cv_id)
        
        print(f"📋 CV数据加载完成，总数: {self.cv_combo.count()}")
        
        # 验证数据
        print(f"\n🔍 验证加载的数据:")
        for i in range(min(5, self.cv_combo.count())):  # 只显示前5个
            text = self.cv_combo.itemText(i)
            data = self.cv_combo.itemData(i)
            print(f"   项目 {i}: '{text}' (数据: {data})")
        
        self.selection_label.setText("当前选择: 无 (数据已重新加载)")
    
    def clear_search(self):
        """清空搜索"""
        self.cv_combo.lineEdit().clear()
        self.cv_combo.current_search = ""
        self.cv_combo.restore_all_items()
        print("🧹 搜索已清空，恢复所有项目")
    
    def on_selection_changed(self, text):
        """选择变化处理"""
        if text and text != "请选择CV...":
            cv_id = self.cv_combo.currentData()
            self.selection_label.setText(f"当前选择: {text} (ID: {cv_id})")
            print(f"📝 选择变化: '{text}' (ID: {cv_id})")
        else:
            self.selection_label.setText("当前选择: 无")
    
    def get_current_selection(self):
        """获取当前选择"""
        current_text = self.cv_combo.currentText()
        current_data = self.cv_combo.currentData()
        current_index = self.cv_combo.currentIndex()
        
        print(f"\n📊 当前选择信息:")
        print(f"   文本: '{current_text}'")
        print(f"   数据: {current_data}")
        print(f"   索引: {current_index}")
        print(f"   总项目数: {self.cv_combo.count()}")
        print(f"   当前显示项目数: {super(SearchableComboBox, self.cv_combo).count()}")
        
        from PyQt5.QtWidgets import QMessageBox
        info = f"当前选择:\n\n文本: {current_text}\n数据: {current_data}\n索引: {current_index}"
        QMessageBox.information(self, "当前选择", info)


def test_basic_functionality():
    """测试基本功能"""
    print("🔍 测试SearchableComboBox基本功能")
    
    try:
        # 创建应用程序
        app = QApplication([])
        
        # 创建SearchableComboBox
        combo = SearchableComboBox()
        print("✅ SearchableComboBox创建成功")
        
        # 测试addItem
        combo.addItem("请选择CV...")
        combo.addItem("张三", "cv_001")
        combo.addItem("李明轩", "cv_002")
        combo.addItem("王诗涵", "cv_003")
        
        print(f"✅ 添加项目成功，数量: {combo.count()}")
        
        # 测试数据获取
        for i in range(combo.count()):
            text = combo.itemText(i)
            data = combo.itemData(i)
            print(f"   项目 {i}: '{text}' (数据: {data})")
        
        # 测试搜索功能
        print("\n🔍 测试搜索功能:")
        combo.lineEdit().setText("z")
        print("   输入 'z' 触发搜索")
        
        # 等待搜索完成
        import time
        time.sleep(0.5)
        
        print("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🎯 修复后的SearchableComboBox测试")
    print("="*50)
    
    # 先测试基本功能
    if not test_basic_functionality():
        print("❌ 基本功能测试失败，退出")
        return
    
    print("\n" + "="*50)
    print("🖥️ 启动GUI测试窗口...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestFixedComboBoxWindow()
    window.show()
    
    print("✅ GUI测试窗口已启动")
    print("\n💡 测试说明:")
    print("  1. 下拉框应该显示所有CV项目")
    print("  2. 点击下拉按钮应该正常展开列表")
    print("  3. 输入拼音首字母应该实时筛选 (如 'z', 'zs', 'lm')")
    print("  4. 选择项目应该正确显示和获取数据")
    print("  5. 按Escape键或点击'清空搜索'恢复所有项目")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
