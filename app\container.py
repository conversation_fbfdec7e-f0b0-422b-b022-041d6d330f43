"""依赖注入容器模块 - 更新版

此模块实现了应用程序的依赖注入容器，管理所有服务的创建和依赖关系。
"""

from typing import Dict, Any, TypeVar, Type, Callable
from infrastructure.api.gstudios_api_service import GStudiosAPIService
from infrastructure.persistence.json_config_repository import JsonConfigRepository
from infrastructure.repositories.gstudios_character_repository import GStudiosCharacterRepository
from domain.services.cv_matcher import CVMatcher
from domain.repositories.character_repository import CharacterRepository
from domain.repositories.cv_repository import CVRepository
from domain.repositories.nickname_repository import NicknameRepository
from application.use_cases.get_characters_use_case import GetCharactersUseCase
from application.use_cases.assign_cv_use_case import AssignCVUseCase
from application.use_cases.get_books_use_case import GetBooksUseCase
from application.character_application_service import CharacterApplicationService
from application.book_application_service import BookApplicationService

T = TypeVar('T')


class DIContainer:
    """依赖注入容器"""
    
    def __init__(self):
        """初始化容器"""
        self._services: Dict[Type, Any] = {}
        self._factories: Dict[Type, Callable] = {}
        self._singletons: Dict[Type, Any] = {}
        self._configure_services()
    
    def _configure_services(self):
        """配置服务依赖关系"""
        # 配置基础设施服务
        self.register_factory(JsonConfigRepository, self._create_config_repository)
        # 将API服务注册为单例，确保令牌设置在所有地方都生效
        self.register_singleton(GStudiosAPIService, self._create_api_service())

        # 配置仓储
        self.register_factory(CharacterRepository, self._create_character_repository)
        # 暂时注释掉未实现的服务
        # self.register_factory(CVRepository, self._create_cv_repository)
        # self.register_factory(NicknameRepository, self._create_nickname_repository)

        # 配置领域服务
        # self.register_factory(CVMatcher, self._create_cv_matcher)

        # 配置应用用例
        self.register_factory(GetCharactersUseCase, self._create_get_characters_use_case)
        self.register_factory(GetBooksUseCase, self._create_get_books_use_case)
        # 暂时注释掉依赖未实现服务的用例
        # self.register_factory(AssignCVUseCase, self._create_assign_cv_use_case)

        # 配置应用服务
        self.register_factory(CharacterApplicationService, self._create_character_app_service_simple)
        self.register_factory(BookApplicationService, self._create_book_app_service)
    
    def register_singleton(self, interface: Type[T], instance: T):
        """注册单例服务"""
        self._singletons[interface] = instance
    
    def register_factory(self, interface: Type[T], factory: Callable[[], T]):
        """注册工厂方法"""
        self._factories[interface] = factory
    
    def get(self, interface: Type[T]) -> T:
        """获取服务实例"""
        # 检查单例
        if interface in self._singletons:
            return self._singletons[interface]
        
        # 检查工厂
        if interface in self._factories:
            return self._factories[interface]()
        
        # 检查已注册的服务
        if interface in self._services:
            return self._services[interface]
        
        raise ValueError(f"Service {interface} not registered")
    
    def _create_config_repository(self) -> JsonConfigRepository:
        """创建配置仓储"""
        return JsonConfigRepository()
    
    def _create_api_service(self) -> GStudiosAPIService:
        """创建API服务"""
        config_repo = self.get(JsonConfigRepository)
        base_url = config_repo.get('API', 'base_url', 'https://www.gstudios.com.cn/story_v2/api')
        token = config_repo.get('API', 'default_token')
        return GStudiosAPIService(base_url, token)
    
    def _create_character_repository(self) -> CharacterRepository:
        """创建角色仓储"""
        api_service = self.get(GStudiosAPIService)
        return GStudiosCharacterRepository(api_service)

    def _create_cv_repository(self) -> CVRepository:
        """创建CV仓储"""
        # 这里应该创建一个CV仓储的实现
        # 暂时返回一个简单的实现或抛出异常
        raise NotImplementedError("CV仓储实现尚未完成")

    def _create_nickname_repository(self) -> NicknameRepository:
        """创建简名仓储"""
        # 这里应该创建一个简名仓储的实现
        # 暂时返回一个简单的实现或抛出异常
        raise NotImplementedError("简名仓储实现尚未完成")
    
    def _create_cv_matcher(self) -> CVMatcher:
        """创建CV匹配器"""
        nickname_repo = self.get(NicknameRepository)
        return CVMatcher(nickname_repo)
    
    def _create_get_characters_use_case(self) -> GetCharactersUseCase:
        """创建获取角色用例"""
        character_repo = self.get(CharacterRepository)
        return GetCharactersUseCase(character_repo)

    def _create_get_books_use_case(self) -> GetBooksUseCase:
        """创建获取书籍用例"""
        api_service = self.get(GStudiosAPIService)
        return GetBooksUseCase(api_service)
    
    def _create_assign_cv_use_case(self) -> AssignCVUseCase:
        """创建分配CV用例"""
        character_repo = self.get(CharacterRepository)
        cv_repo = self.get(CVRepository)
        return AssignCVUseCase(character_repo, cv_repo)
    
    def _create_character_app_service(self) -> CharacterApplicationService:
        """创建角色应用服务"""
        get_characters_use_case = self.get(GetCharactersUseCase)
        assign_cv_use_case = self.get(AssignCVUseCase)
        return CharacterApplicationService(get_characters_use_case, assign_cv_use_case)

    def _create_character_app_service_simple(self) -> CharacterApplicationService:
        """创建简化版角色应用服务（仅支持获取角色）"""
        get_characters_use_case = self.get(GetCharactersUseCase)
        # 暂时传入None作为assign_cv_use_case，需要修改CharacterApplicationService来处理这种情况
        return CharacterApplicationService(get_characters_use_case, None)

    def _create_book_app_service(self) -> BookApplicationService:
        """创建书籍应用服务"""
        get_books_use_case = self.get(GetBooksUseCase)
        return BookApplicationService(get_books_use_case)


# 全局容器实例
container = DIContainer()
