#!/usr/bin/env python3
"""测试CV下拉框焦点保持行为

专门测试输入框字母变化后，下拉框显示时焦点是否保持在输入框中。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import Qt, QTimer
from anti_flicker_combobox import AntiFlickerComboBox


class FocusTestWindow(QMainWindow):
    """焦点行为测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_test_data()
        self.setup_focus_monitoring()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("CV下拉框焦点保持测试")
        self.setGeometry(100, 100, 600, 500)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("CV下拉框焦点保持测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 测试说明
        info_label = QLabel(
            "🎯 焦点保持测试:\n"
            "• 点击输入框获得焦点，下拉列表应自动显示\n"
            "• 输入字母后，下拉列表显示但焦点应保持在输入框\n"
            "• 点击下拉按钮后，焦点应保持在输入框\n"
            "• 选择项目后，焦点应回到输入框\n"
            "• 观察下方的焦点状态实时显示"
        )
        info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # CV下拉框
        layout.addWidget(QLabel("测试CV下拉框:"))
        self.cv_combo = AntiFlickerComboBox()
        layout.addWidget(self.cv_combo)
        
        # 焦点状态显示
        self.focus_status = QLabel("焦点状态: 未知")
        self.focus_status.setStyleSheet("margin: 10px; padding: 5px; background: #e8f4fd; border-radius: 3px; font-weight: bold;")
        layout.addWidget(self.focus_status)
        
        # 当前选择显示
        self.selection_label = QLabel("当前选择: 无")
        self.selection_label.setStyleSheet("margin: 10px; padding: 5px; background: #f0f8ff; border-radius: 3px;")
        layout.addWidget(self.selection_label)
        
        # 测试按钮
        button_layout = QVBoxLayout()
        
        focus_test_button = QPushButton("手动设置焦点到输入框")
        focus_test_button.clicked.connect(self.set_focus_to_input)
        button_layout.addWidget(focus_test_button)
        
        clear_button = QPushButton("清空输入框")
        clear_button.clicked.connect(self.clear_input)
        button_layout.addWidget(clear_button)
        
        show_popup_button = QPushButton("手动显示下拉列表")
        show_popup_button.clicked.connect(self.show_popup_manually)
        button_layout.addWidget(show_popup_button)
        
        layout.addLayout(button_layout)
        
        # 焦点事件日志
        layout.addWidget(QLabel("焦点事件日志:"))
        self.focus_log = QTextEdit()
        self.focus_log.setMaximumHeight(150)
        self.focus_log.setStyleSheet("font-family: monospace; font-size: 12px;")
        layout.addWidget(self.focus_log)
        
        layout.addStretch()
        
        # 连接信号
        self.cv_combo.currentTextChanged.connect(self.on_selection_changed)
    
    def load_test_data(self):
        """加载测试数据"""
        # 清空现有数据
        self.cv_combo.clear()
        
        # 模拟CV数据
        test_cvs = [
            {"name": "张三", "id": "cv_001"},
            {"name": "李明轩", "id": "cv_002"},
            {"name": "王诗涵", "id": "cv_003"},
            {"name": "陈奕迅", "id": "cv_004"},
            {"name": "小红", "id": "cv_005"},
            {"name": "赵钱孙", "id": "cv_006"},
            {"name": "周杰伦", "id": "cv_007"},
            {"name": "刘德华", "id": "cv_008"},
        ]
        
        # 添加默认选项
        self.cv_combo.addItem("请选择CV...")
        
        # 添加CV数据
        for cv in test_cvs:
            self.cv_combo.addItem(cv["name"], cv["id"])
        
        self.log_focus_event("数据加载完成")
    
    def setup_focus_monitoring(self):
        """设置焦点监控"""
        # 创建定时器监控焦点状态
        self.focus_timer = QTimer()
        self.focus_timer.timeout.connect(self.update_focus_status)
        self.focus_timer.start(100)  # 每100ms检查一次焦点状态
    
    def update_focus_status(self):
        """更新焦点状态显示"""
        if self.cv_combo.lineEdit().hasFocus():
            self.focus_status.setText("焦点状态: ✅ 在输入框中")
            self.focus_status.setStyleSheet("margin: 10px; padding: 5px; background: #d4edda; border-radius: 3px; font-weight: bold; color: #155724;")
        elif self.cv_combo.hasFocus():
            self.focus_status.setText("焦点状态: ⚠️ 在ComboBox上")
            self.focus_status.setStyleSheet("margin: 10px; padding: 5px; background: #fff3cd; border-radius: 3px; font-weight: bold; color: #856404;")
        else:
            self.focus_status.setText("焦点状态: ❌ 不在CV下拉框")
            self.focus_status.setStyleSheet("margin: 10px; padding: 5px; background: #f8d7da; border-radius: 3px; font-weight: bold; color: #721c24;")
    
    def log_focus_event(self, event_description):
        """记录焦点事件"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        focus_info = ""
        if self.cv_combo.lineEdit().hasFocus():
            focus_info = "✅ 输入框有焦点"
        elif self.cv_combo.hasFocus():
            focus_info = "⚠️ ComboBox有焦点"
        else:
            focus_info = "❌ 无焦点"
        
        log_entry = f"[{timestamp}] {event_description} - {focus_info}"
        self.focus_log.append(log_entry)
        
        # 自动滚动到底部
        cursor = self.focus_log.textCursor()
        cursor.movePosition(cursor.End)
        self.focus_log.setTextCursor(cursor)
    
    def set_focus_to_input(self):
        """手动设置焦点到输入框"""
        self.cv_combo.lineEdit().setFocus()
        self.log_focus_event("手动设置焦点到输入框")
    
    def clear_input(self):
        """清空输入框"""
        self.cv_combo.lineEdit().clear()
        self.log_focus_event("清空输入框")
    
    def show_popup_manually(self):
        """手动显示下拉列表"""
        self.cv_combo.showPopup()
        self.log_focus_event("手动显示下拉列表")
        
        # 延迟检查焦点状态
        QTimer.singleShot(100, lambda: self.log_focus_event("下拉列表显示后"))
    
    def on_selection_changed(self, text):
        """选择变化处理"""
        if text and text != "请选择CV...":
            cv_id = self.cv_combo.currentData()
            self.selection_label.setText(f"当前选择: {text} (ID: {cv_id})")
            self.log_focus_event(f"选择了项目: {text}")
        else:
            self.selection_label.setText("当前选择: 无")
            self.log_focus_event("清空了选择")


def main():
    """主函数"""
    print("🎯 CV下拉框焦点保持测试")
    print("="*50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = FocusTestWindow()
    window.show()
    
    print("✅ 焦点测试窗口已启动")
    print("\n💡 测试步骤:")
    print("  1. 点击CV下拉框的输入区域")
    print("  2. 观察焦点状态是否显示'✅ 在输入框中'")
    print("  3. 输入字母 'z' 或其他拼音首字母")
    print("  4. 观察下拉列表显示后焦点是否仍在输入框")
    print("  5. 点击下拉按钮，观察焦点状态")
    print("  6. 选择一个CV项目，观察焦点是否回到输入框")
    print("  7. 查看下方的焦点事件日志了解详细情况")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
