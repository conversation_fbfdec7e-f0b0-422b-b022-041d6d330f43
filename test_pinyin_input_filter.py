#!/usr/bin/env python3
"""测试拼音输入筛选功能

此脚本测试新增的拼音首字母输入筛选功能。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.pinyin_helper import PinyinHelper


def test_get_text_pinyin_letters():
    """测试获取文本拼音首字母组合功能"""
    print("🔍 测试获取文本拼音首字母组合功能...")
    
    test_cases = [
        ("张三", "ZS"),
        ("李明轩", "LMX"),
        ("王诗涵", "WSH"),
        ("主角", "ZJ"),
        ("女主角", "NZJ"),
        ("配角A", "PJA"),
        ("小明", "XM"),
        ("赵钱孙李", "ZQSL"),
        ("", ""),
        ("123", ""),
        ("ABC", "ABC"),
        ("张A李B", "ZALB"),
    ]
    
    success_count = 0
    for text, expected in test_cases:
        result = PinyinHelper.get_text_pinyin_letters(text)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{text}' → '{result}' (期望: '{expected}')")
        if result == expected:
            success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_filter_by_pinyin_input():
    """测试拼音输入筛选功能"""
    print("\n🔍 测试拼音输入筛选功能...")
    
    # 模拟角色数据
    class MockCharacter:
        def __init__(self, name):
            self.name = name
        
        def __repr__(self):
            return f"Character('{self.name}')"
    
    characters = [
        MockCharacter("张三"),
        MockCharacter("张小雨"),
        MockCharacter("李明"),
        MockCharacter("李明轩"),
        MockCharacter("王诗涵"),
        MockCharacter("主角"),
        MockCharacter("女主角"),
        MockCharacter("配角A"),
        MockCharacter("赵钱孙"),
        MockCharacter("周杰伦"),
    ]
    
    test_cases = [
        ("z", ["张三", "张小雨", "主角", "赵钱孙", "周杰伦"]),  # Z开头
        ("zs", ["张三"]),  # 张三
        ("zx", ["张小雨"]),  # 张小雨
        ("lm", ["李明", "李明轩"]),  # 李明开头
        ("lmx", ["李明轩"]),  # 李明轩
        ("nz", ["女主角"]),  # 女主角
        ("pj", ["配角A"]),  # 配角
        ("", [c.name for c in characters]),  # 空输入显示全部
        ("xyz", []),  # 不存在的组合
        ("ZS", ["张三"]),  # 大写输入
        ("zS", ["张三"]),  # 混合大小写
    ]
    
    success_count = 0
    for input_text, expected_names in test_cases:
        result = PinyinHelper.filter_by_pinyin_input(
            characters, 
            input_text, 
            key_func=lambda char: char.name
        )
        result_names = [char.name for char in result]
        
        status = "✅" if set(result_names) == set(expected_names) else "❌"
        print(f"  {status} 输入 '{input_text}' → {result_names}")
        if set(result_names) != set(expected_names):
            print(f"      期望: {expected_names}")
        
        if set(result_names) == set(expected_names):
            success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况...")
    
    # 测试特殊字符和空值
    edge_cases = [
        ("  ", ""),  # 空格
        ("张三 ", "ZS"),  # 末尾空格
        (" 张三", "ZS"),  # 开头空格
        ("张-三", "ZS"),  # 特殊字符
        ("张三123", "ZS"),  # 数字
        ("张三ABC", "ZSABC"),  # 混合中英文
        ("ABC张三", "ABCZS"),  # 英文开头
        ("123张三", "ZS"),  # 数字开头
    ]
    
    success_count = 0
    for text, expected in edge_cases:
        result = PinyinHelper.get_text_pinyin_letters(text)
        status = "✅" if result == expected else "❌"
        print(f"  {status} '{text}' → '{result}' (期望: '{expected}')")
        if result == expected:
            success_count += 1
    
    print(f"\n📊 边界测试结果: {success_count}/{len(edge_cases)} 通过")
    return success_count == len(edge_cases)


def main():
    """主测试函数"""
    print("🚀 开始测试拼音输入筛选功能\n")
    
    # 运行所有测试
    test1_passed = test_get_text_pinyin_letters()
    test2_passed = test_filter_by_pinyin_input()
    test3_passed = test_edge_cases()
    
    # 总结
    print("\n" + "="*50)
    print("📋 测试总结:")
    print(f"  拼音首字母提取: {'✅ 通过' if test1_passed else '❌ 失败'}")
    print(f"  拼音输入筛选: {'✅ 通过' if test2_passed else '❌ 失败'}")
    print(f"  边界情况测试: {'✅ 通过' if test3_passed else '❌ 失败'}")
    
    all_passed = test1_passed and test2_passed and test3_passed
    print(f"\n🎯 总体结果: {'✅ 所有测试通过' if all_passed else '❌ 部分测试失败'}")
    
    if all_passed:
        print("\n🎉 拼音输入筛选功能实现正确！")
        print("💡 使用说明:")
        print("  - 输入单个字母筛选首字母匹配的角色 (如: z)")
        print("  - 输入多个字母筛选拼音组合匹配的角色 (如: zs)")
        print("  - 输入不区分大小写")
        print("  - 清空输入框显示所有角色")
    else:
        print("\n⚠️ 请检查实现并修复失败的测试用例")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
