#!/usr/bin/env python3
"""对比不同下拉框实现

对比原始ComboBox、AntiFlickerComboBox和CustomDropdownWidget的功能和性能。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget, 
                             QLabel, QPushButton, QHBoxLayout, QComboBox, QGroupBox)
from PyQt5.QtCore import Qt, QTimer
from anti_flicker_combobox import AntiFlickerComboBox
from custom_dropdown_widget import CustomDropdownWidget


class ComparisonTestWindow(QMainWindow):
    """下拉框实现对比测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_test_data()
        self.setup_monitoring()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("下拉框实现对比测试")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("下拉框实现对比测试")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 10px; text-align: center;")
        layout.addWidget(title_label)
        
        # 说明
        info_label = QLabel(
            "🔍 对比测试内容:\n"
            "• 原始QComboBox - 基础功能\n"
            "• AntiFlickerComboBox - 防闪烁优化版本\n"
            "• CustomDropdownWidget - QLineEdit+QListWidget组合\n"
            "请在每个下拉框中测试相同的操作，观察差异"
        )
        info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # 创建三个测试组
        implementations_layout = QHBoxLayout()
        
        # 1. 原始ComboBox
        original_group = QGroupBox("1. 原始QComboBox")
        original_layout = QVBoxLayout(original_group)
        
        original_layout.addWidget(QLabel("基础ComboBox:"))
        self.original_combo = QComboBox()
        self.original_combo.setEditable(True)
        original_layout.addWidget(self.original_combo)
        
        self.original_status = QLabel("状态: 就绪")
        self.original_status.setStyleSheet("margin: 5px; padding: 3px; background: #e8f4fd; border-radius: 3px; font-size: 11px;")
        original_layout.addWidget(self.original_status)
        
        original_layout.addStretch()
        implementations_layout.addWidget(original_group)
        
        # 2. AntiFlickerComboBox
        antiflicker_group = QGroupBox("2. AntiFlickerComboBox")
        antiflicker_layout = QVBoxLayout(antiflicker_group)
        
        antiflicker_layout.addWidget(QLabel("防闪烁ComboBox:"))
        self.antiflicker_combo = AntiFlickerComboBox()
        antiflicker_layout.addWidget(self.antiflicker_combo)
        
        self.antiflicker_status = QLabel("状态: 就绪")
        self.antiflicker_status.setStyleSheet("margin: 5px; padding: 3px; background: #fff3cd; border-radius: 3px; font-size: 11px;")
        antiflicker_layout.addWidget(self.antiflicker_status)
        
        antiflicker_layout.addStretch()
        implementations_layout.addWidget(antiflicker_group)
        
        # 3. CustomDropdownWidget
        custom_group = QGroupBox("3. CustomDropdownWidget")
        custom_layout = QVBoxLayout(custom_group)
        
        custom_layout.addWidget(QLabel("自定义下拉框:"))
        self.custom_dropdown = CustomDropdownWidget()
        self.custom_dropdown.setFixedHeight(30)
        custom_layout.addWidget(self.custom_dropdown)
        
        self.custom_status = QLabel("状态: 就绪")
        self.custom_status.setStyleSheet("margin: 5px; padding: 3px; background: #d4edda; border-radius: 3px; font-size: 11px;")
        custom_layout.addWidget(self.custom_status)
        
        custom_layout.addStretch()
        implementations_layout.addWidget(custom_group)
        
        layout.addLayout(implementations_layout)
        
        # 测试按钮
        button_layout = QHBoxLayout()
        
        load_button = QPushButton("重新加载测试数据")
        load_button.clicked.connect(self.load_test_data)
        button_layout.addWidget(load_button)
        
        clear_button = QPushButton("清空所有输入")
        clear_button.clicked.connect(self.clear_all)
        button_layout.addWidget(clear_button)
        
        test_button = QPushButton("自动测试序列")
        test_button.clicked.connect(self.run_auto_test)
        button_layout.addWidget(test_button)
        
        layout.addLayout(button_layout)
        
        # 测试指南
        guide_label = QLabel(
            "📝 测试指南:\n"
            "1. 点击每个输入框获得焦点，观察下拉列表显示行为\n"
            "2. 输入字母 'z', 'zs', 'lm' 等，观察搜索和筛选效果\n"
            "3. 使用上下箭头键导航，观察键盘响应\n"
            "4. 按回车键选择项目，观察选择行为\n"
            "5. 按Escape键清空搜索，观察清空效果\n"
            "6. 点击下拉按钮（如果有），观察显示效果\n"
            "7. 快速连续输入字母，观察是否有闪烁或延迟"
        )
        guide_label.setStyleSheet("color: #444; margin: 10px; padding: 10px; background: #f9f9f9; border-radius: 5px; font-size: 12px;")
        layout.addWidget(guide_label)
        
        layout.addStretch()
        
        # 连接信号
        self.original_combo.currentTextChanged.connect(lambda text: self.update_status(self.original_status, f"文本: '{text}'"))
        self.antiflicker_combo.currentTextChanged.connect(lambda text: self.update_status(self.antiflicker_status, f"文本: '{text}'"))
        self.custom_dropdown.currentTextChanged.connect(lambda text: self.update_status(self.custom_status, f"文本: '{text}'"))
        
        self.custom_dropdown.itemSelected.connect(lambda text, data: self.update_status(self.custom_status, f"选择: '{text}' (ID: {data})"))
    
    def load_test_data(self):
        """加载测试数据"""
        # 测试CV数据
        test_cvs = [
            {"name": "张三", "id": "cv_001"},
            {"name": "张学友", "id": "cv_002"},
            {"name": "李明轩", "id": "cv_003"},
            {"name": "李小龙", "id": "cv_004"},
            {"name": "王诗涵", "id": "cv_005"},
            {"name": "王菲", "id": "cv_006"},
            {"name": "陈奕迅", "id": "cv_007"},
            {"name": "周杰伦", "id": "cv_008"},
            {"name": "刘德华", "id": "cv_009"},
        ]
        
        # 清空所有下拉框
        self.original_combo.clear()
        self.antiflicker_combo.clear()
        self.custom_dropdown.clear()
        
        # 添加默认选项
        self.original_combo.addItem("请选择CV...")
        self.antiflicker_combo.addItem("请选择CV...")
        self.custom_dropdown.addItem("请选择CV...")
        
        # 添加CV数据到所有下拉框
        for cv in test_cvs:
            self.original_combo.addItem(cv["name"])
            self.antiflicker_combo.addItem(cv["name"], cv["id"])
            self.custom_dropdown.addItem(cv["name"], cv["id"])
        
        # 更新状态
        self.update_status(self.original_status, f"已加载 {len(test_cvs)} 个CV")
        self.update_status(self.antiflicker_status, f"已加载 {len(test_cvs)} 个CV")
        self.update_status(self.custom_status, f"已加载 {len(test_cvs)} 个CV")
    
    def setup_monitoring(self):
        """设置监控"""
        # 创建定时器监控状态
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_focus_status)
        self.monitor_timer.start(200)  # 每200ms检查一次
    
    def update_focus_status(self):
        """更新焦点状态"""
        # 检查哪个控件有焦点
        if self.original_combo.hasFocus() or self.original_combo.lineEdit().hasFocus():
            self.original_status.setStyleSheet("margin: 5px; padding: 3px; background: #d4edda; border-radius: 3px; font-size: 11px; color: #155724;")
        else:
            self.original_status.setStyleSheet("margin: 5px; padding: 3px; background: #e8f4fd; border-radius: 3px; font-size: 11px;")
        
        if self.antiflicker_combo.hasFocus() or (hasattr(self.antiflicker_combo, 'lineEdit') and self.antiflicker_combo.lineEdit().hasFocus()):
            self.antiflicker_status.setStyleSheet("margin: 5px; padding: 3px; background: #d4edda; border-radius: 3px; font-size: 11px; color: #155724;")
        else:
            self.antiflicker_status.setStyleSheet("margin: 5px; padding: 3px; background: #fff3cd; border-radius: 3px; font-size: 11px;")
        
        if self.custom_dropdown.hasFocus():
            self.custom_status.setStyleSheet("margin: 5px; padding: 3px; background: #d4edda; border-radius: 3px; font-size: 11px; color: #155724;")
        else:
            self.custom_status.setStyleSheet("margin: 5px; padding: 3px; background: #d4edda; border-radius: 3px; font-size: 11px;")
    
    def update_status(self, status_label, message):
        """更新状态标签"""
        status_label.setText(f"状态: {message}")
    
    def clear_all(self):
        """清空所有输入"""
        self.original_combo.setCurrentText("")
        if hasattr(self.antiflicker_combo, 'setCurrentText'):
            self.antiflicker_combo.setCurrentText("")
        self.custom_dropdown.setCurrentText("")
        
        self.update_status(self.original_status, "已清空")
        self.update_status(self.antiflicker_status, "已清空")
        self.update_status(self.custom_status, "已清空")
    
    def run_auto_test(self):
        """运行自动测试序列"""
        self.update_status(self.original_status, "开始自动测试...")
        self.update_status(self.antiflicker_status, "开始自动测试...")
        self.update_status(self.custom_status, "开始自动测试...")
        
        # 模拟输入测试
        QTimer.singleShot(500, lambda: self.simulate_input(self.original_combo, "张"))
        QTimer.singleShot(1000, lambda: self.simulate_input(self.antiflicker_combo, "张"))
        QTimer.singleShot(1500, lambda: self.simulate_input(self.custom_dropdown, "张"))
        
        QTimer.singleShot(2500, lambda: self.update_status(self.original_status, "自动测试完成"))
        QTimer.singleShot(2500, lambda: self.update_status(self.antiflicker_status, "自动测试完成"))
        QTimer.singleShot(2500, lambda: self.update_status(self.custom_status, "自动测试完成"))
    
    def simulate_input(self, widget, text):
        """模拟输入"""
        if hasattr(widget, 'setCurrentText'):
            widget.setCurrentText(text)
        elif hasattr(widget, 'lineEdit'):
            widget.lineEdit().setText(text)
        else:
            widget.setEditText(text)


def main():
    """主函数"""
    print("🔍 下拉框实现对比测试")
    print("="*50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = ComparisonTestWindow()
    window.show()
    
    print("✅ 对比测试窗口已启动")
    print("\n💡 对比要点:")
    print("  1. 功能完整性 - 所有基本功能是否正常")
    print("  2. 交互响应性 - 键盘输入和鼠标操作的响应")
    print("  3. 视觉效果 - 下拉列表的显示和隐藏")
    print("  4. 搜索功能 - 拼音搜索的准确性和速度")
    print("  5. 稳定性 - 是否有闪烁、卡顿或异常")
    print("  6. 用户体验 - 整体使用的流畅度")
    
    print("\n🎯 预期结果:")
    print("  • 原始QComboBox: 基础功能，可能有交互问题")
    print("  • AntiFlickerComboBox: 改进的交互，但仍有ComboBox限制")
    print("  • CustomDropdownWidget: 完全控制的交互，最佳用户体验")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
