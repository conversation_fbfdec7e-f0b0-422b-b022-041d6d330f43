"""可搜索的ComboBox组件

支持拼音首字母搜索的下拉框组件。
"""

from PyQt5.QtWidgets import QComboBox, QCompleter
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QStandardItemModel, QStandardItem
from utils.pinyin_helper import PinyinHelper


class SearchableComboBox(QComboBox):
    """支持拼音搜索的ComboBox"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 存储原始数据
        self.original_items = []
        self.original_data = []
        self.all_items = []  # 存储所有项目的完整信息

        self.setup_search_functionality()

    def setup_search_functionality(self):
        """设置搜索功能"""
        # 设置为可编辑
        self.setEditable(True)
        self.setInsertPolicy(QComboBox.NoInsert)

        # 连接编辑信号
        self.lineEdit().textChanged.connect(self.on_text_changed)

        # 设置定时器用于延迟搜索
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)

        # 当前搜索文本
        self.current_search = ""

    def on_text_changed(self, text):
        """文本变化处理"""
        self.current_search = text.strip()

        # 使用定时器延迟搜索，避免频繁更新
        self.search_timer.stop()
        self.search_timer.start(300)  # 300ms延迟

    def perform_search(self):
        """执行搜索"""
        search_text = self.current_search

        if not search_text:
            # 如果搜索为空，恢复所有项目
            self.restore_all_items()
            return

        # 筛选匹配的项目
        filtered_items = []
        for item_info in self.all_items:
            text = item_info['text']
            data = item_info['data']

            # 跳过默认提示项
            if text in ["请选择CV...", "请先选择书籍...", "CV加载失败", "暂无可用CV"]:
                filtered_items.append(item_info)
                continue

            # 使用拼音助手进行匹配
            if self.matches_search(text, search_text):
                filtered_items.append(item_info)

        # 更新ComboBox内容
        self.update_items(filtered_items)

        # 如果有匹配结果且输入框有内容，显示下拉列表
        if filtered_items and search_text:
            if not self.view().isVisible():
                self.showPopup()

    def matches_search(self, text, search_text):
        """检查文本是否匹配搜索"""
        if not search_text:
            return True

        # 使用拼音助手进行匹配
        item_pinyin = PinyinHelper.get_text_pinyin_letters(text)
        search_upper = search_text.upper()

        # 支持多种匹配方式
        return (search_upper in item_pinyin or                    # 拼音首字母匹配
                search_upper in text.upper() or                   # 直接文本匹配
                item_pinyin.startswith(search_upper))              # 拼音开头匹配

    def addItem(self, text, userData=None):
        """添加项目"""
        # 保存到所有项目列表
        item_info = {'text': text, 'data': userData}
        self.all_items.append(item_info)

        # 保存原始数据（兼容性）
        self.original_items.append(text)
        self.original_data.append(userData)

        # 直接添加到ComboBox
        super().addItem(text)

        # 设置数据
        if userData is not None:
            super().setItemData(super().count() - 1, userData, Qt.UserRole)

    def clear(self):
        """清空所有项目"""
        self.all_items.clear()
        self.original_items.clear()
        self.original_data.clear()
        self.current_search = ""
        super().clear()

    def update_items(self, items):
        """更新ComboBox中的项目"""
        # 保存当前输入
        current_text = self.lineEdit().text()

        # 清空并重新添加
        super().clear()

        for item_info in items:
            super().addItem(item_info['text'])
            if item_info['data'] is not None:
                super().setItemData(super().count() - 1, item_info['data'], Qt.UserRole)

        # 恢复输入
        self.lineEdit().setText(current_text)

    def restore_all_items(self):
        """恢复所有项目"""
        self.update_items(self.all_items)

    def currentText(self):
        """获取当前文本"""
        return self.lineEdit().text()

    def currentData(self, role=Qt.UserRole):
        """获取当前数据"""
        current_text = self.currentText()

        # 在原始数据中查找匹配的项目
        for i, item_text in enumerate(self.original_items):
            if item_text == current_text:
                return self.original_data[i] if i < len(self.original_data) else None

        return None

    def setCurrentText(self, text):
        """设置当前文本"""
        self.lineEdit().setText(text)

        # 清空搜索并恢复所有项目
        self.current_search = ""
        self.restore_all_items()

    def count(self):
        """获取项目数量"""
        return len(self.original_items)

    def itemText(self, index):
        """获取指定索引的文本"""
        if 0 <= index < len(self.original_items):
            return self.original_items[index]
        return ""

    def itemData(self, index, role=Qt.UserRole):
        """获取指定索引的数据"""
        if 0 <= index < len(self.original_data):
            return self.original_data[index]
        return None

    def setItemData(self, index, value, role=Qt.UserRole):
        """设置指定索引的数据"""
        if 0 <= index < len(self.original_data):
            self.original_data[index] = value

            # 同时更新all_items中的数据
            if 0 <= index < len(self.all_items):
                self.all_items[index]['data'] = value

    def findText(self, text, flags=Qt.MatchExactly):
        """查找文本对应的索引"""
        for i, item_text in enumerate(self.original_items):
            if flags == Qt.MatchExactly and item_text == text:
                return i
            elif flags == Qt.MatchContains and text in item_text:
                return i
        return -1

    def setCurrentIndex(self, index):
        """设置当前索引"""
        if 0 <= index < len(self.original_items):
            self.setCurrentText(self.original_items[index])

    def currentIndex(self):
        """获取当前索引"""
        current_text = self.currentText()
        return self.findText(current_text)

    def keyPressEvent(self, event):
        """键盘事件处理"""
        # 如果按下Escape键，清空搜索并恢复所有项目
        if event.key() == Qt.Key_Escape:
            self.lineEdit().clear()
            self.current_search = ""
            self.restore_all_items()
            self.hidePopup()
            return

        super().keyPressEvent(event)

    def focusOutEvent(self, event):
        """失去焦点事件处理"""
        # 失去焦点时恢复所有项目
        if self.current_search:
            self.current_search = ""
            self.restore_all_items()

        super().focusOutEvent(event)