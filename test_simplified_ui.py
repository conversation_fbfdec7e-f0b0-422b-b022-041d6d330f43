#!/usr/bin/env python3
"""测试简化后的UI功能

此脚本测试移除A-Z按钮后的拼音筛选功能。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.pinyin_helper import PinyinHelper


def test_character_filtering():
    """测试角色筛选功能"""
    print("🔍 测试角色筛选功能\n")
    
    # 模拟角色数据
    class MockCharacter:
        def __init__(self, name, cv_name=None):
            self.name = name
            self.cv_name = cv_name
        
        def __repr__(self):
            cv_info = f" (CV: {self.cv_name})" if self.cv_name else ""
            return f"{self.name}{cv_info}"
    
    characters = [
        MockCharacter("张三", "张小雨"),
        MockCharacter("李明轩", "李娜"),
        MockCharacter("王诗涵", "王小明"),
        MockCharacter("主角"),
        MockCharacter("女主角", "陈奕迅"),
        MockCharacter("配角A"),
        <PERSON>ckCharacter("小明", "小红"),
        MockCharacter("赵钱孙"),
        MockCharacter("周杰伦"),
        MockCharacter("陈奕迅"),
    ]
    
    print("📋 角色列表:")
    for i, char in enumerate(characters, 1):
        pinyin = PinyinHelper.get_text_pinyin_letters(char.name)
        print(f"  {i:2d}. {char.name:8} → {pinyin}")
    
    # 测试不同的筛选输入
    test_cases = [
        ("", "显示全部角色"),
        ("z", "筛选包含Z的角色"),
        ("zs", "筛选张三"),
        ("lm", "筛选李明相关"),
        ("wx", "筛选王小明"),
        ("cy", "筛选陈奕迅"),
        ("abc", "不存在的组合"),
    ]
    
    print(f"\n{'='*50}")
    print("🎯 筛选测试结果:")
    
    for input_text, description in test_cases:
        print(f"\n📝 {description}")
        print(f"   输入: '{input_text}'")
        
        if not input_text:
            filtered = characters
        else:
            filtered = PinyinHelper.filter_by_pinyin_input(
                characters, 
                input_text,
                key_func=lambda char: char.name
            )
        
        if filtered:
            print(f"   结果: {[char.name for char in filtered]} (共{len(filtered)}个)")
        else:
            print(f"   结果: 无匹配结果")


def test_cv_filtering():
    """测试CV筛选功能"""
    print(f"\n\n🔍 测试CV筛选功能\n")
    
    # 模拟CV数据
    cvs = [
        {"id": "cv_001", "name": "张小雨"},
        {"id": "cv_002", "name": "李娜"},
        {"id": "cv_003", "name": "王小明"},
        {"id": "cv_004", "name": "陈奕迅"},
        {"id": "cv_005", "name": "小红"},
        {"id": "cv_006", "name": "赵钱孙"},
        {"id": "cv_007", "name": "周杰伦"},
        {"id": "cv_008", "name": "刘德华"},
    ]
    
    print("📋 CV列表:")
    for i, cv in enumerate(cvs, 1):
        name = cv.get('name', '')
        pinyin = PinyinHelper.get_text_pinyin_letters(name)
        print(f"  {i:2d}. {name:8} → {pinyin}")
    
    # 测试CV筛选
    test_cases = [
        ("", "显示全部CV"),
        ("z", "筛选包含Z的CV"),
        ("l", "筛选包含L的CV"),
        ("wx", "筛选王小明"),
        ("cy", "筛选陈奕迅"),
        ("ldh", "筛选刘德华"),
    ]
    
    print(f"\n{'='*50}")
    print("🎯 CV筛选测试结果:")
    
    for input_text, description in test_cases:
        print(f"\n📝 {description}")
        print(f"   输入: '{input_text}'")
        
        if not input_text:
            filtered = cvs
        else:
            filtered = PinyinHelper.filter_by_pinyin_input(
                cvs, 
                input_text,
                key_func=lambda cv: cv.get('name', '')
            )
        
        if filtered:
            names = [cv.get('name', '') for cv in filtered]
            print(f"   结果: {names} (共{len(filtered)}个)")
        else:
            print(f"   结果: 无匹配结果")


def test_ui_simulation():
    """模拟UI交互测试"""
    print(f"\n\n🖥️ 模拟UI交互测试\n")
    
    # 模拟主窗口的筛选逻辑
    class MockMainWindow:
        def __init__(self):
            # 模拟角色数据
            self.all_characters = [
                {"name": "张三"}, {"name": "李明"}, {"name": "王诗涵"},
                {"name": "主角"}, {"name": "配角A"}, {"name": "小明"}
            ]
            self.characters = self.all_characters.copy()
            
            # 模拟CV数据
            self.all_cvs = [
                {"name": "张小雨"}, {"name": "李娜"}, {"name": "王小明"},
                {"name": "陈奕迅"}, {"name": "小红"}
            ]
            self.cvs = self.all_cvs.copy()
        
        def on_character_pinyin_input_changed(self, input_text):
            """模拟角色拼音输入变化处理"""
            input_text = input_text.strip()
            
            if not input_text:
                self.characters = self.all_characters.copy()
                message = f"显示全部 {len(self.characters)} 个角色"
            else:
                self.characters = PinyinHelper.filter_by_pinyin_input(
                    self.all_characters,
                    input_text,
                    key_func=lambda char: char['name']
                )
                
                total = len(self.all_characters)
                filtered = len(self.characters)
                if filtered == 0:
                    message = f"没有找到拼音首字母包含 '{input_text.upper()}' 的角色"
                else:
                    message = f"显示拼音首字母包含 '{input_text.upper()}' 的角色: {filtered}/{total}"
            
            return message
        
        def on_cv_pinyin_input_changed(self, input_text):
            """模拟CV拼音输入变化处理"""
            input_text = input_text.strip()
            
            if not input_text:
                self.cvs = self.all_cvs.copy()
                message = f"显示全部 {len(self.cvs)} 个CV"
            else:
                self.cvs = PinyinHelper.filter_by_pinyin_input(
                    self.all_cvs,
                    input_text,
                    key_func=lambda cv: cv['name']
                )
                
                total = len(self.all_cvs)
                filtered = len(self.cvs)
                if filtered == 0:
                    message = f"没有找到拼音首字母包含 '{input_text.upper()}' 的CV"
                else:
                    message = f"显示拼音首字母包含 '{input_text.upper()}' 的CV: {filtered}/{total}"
            
            return message
    
    # 创建模拟窗口
    window = MockMainWindow()
    
    print("🎮 模拟用户交互:")
    
    # 测试角色筛选
    print("\n📝 角色筛选测试:")
    character_inputs = ["", "z", "zs", "lm", "xyz"]
    
    for input_text in character_inputs:
        message = window.on_character_pinyin_input_changed(input_text)
        names = [char['name'] for char in window.characters]
        print(f"   输入 '{input_text}' → {names}")
        print(f"   状态: {message}")
    
    # 测试CV筛选
    print("\n📝 CV筛选测试:")
    cv_inputs = ["", "z", "l", "cy", "abc"]
    
    for input_text in cv_inputs:
        message = window.on_cv_pinyin_input_changed(input_text)
        names = [cv['name'] for cv in window.cvs]
        print(f"   输入 '{input_text}' → {names}")
        print(f"   状态: {message}")


def main():
    """主函数"""
    print("🎯 简化UI功能测试\n")
    print("="*60)
    
    try:
        test_character_filtering()
        test_cv_filtering()
        test_ui_simulation()
        
        print("\n" + "="*60)
        print("✅ 所有测试完成！")
        print("\n💡 简化后的功能特点:")
        print("  ✅ 移除了A-Z字母按钮，界面更简洁")
        print("  ✅ 保留了文本输入筛选功能，更高效")
        print("  ✅ 为CV列表也添加了拼音筛选功能")
        print("  ✅ 筛选逻辑统一，用户体验一致")
        print("  ✅ 代码结构更清晰，维护更容易")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
