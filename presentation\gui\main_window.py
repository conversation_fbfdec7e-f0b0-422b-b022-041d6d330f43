"""主窗口模块

此模块实现了CV分配工具的主窗口界面。
"""

import sys
from typing import List, Optional
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QComboBox, QPushButton, QTableWidget, QTableWidgetItem,
    QMessageBox, QProgressBar, QStatusBar, QSplitter, QGroupBox,
    QHeaderView, QAbstractItemView, QMenuBar, QAction, QLineEdit
)
from presentation.gui.searchable_combobox import SearchableComboBox
from anti_flicker_combobox import AntiFlickerComboBox
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QIcon, QColor

from .controllers.character_controller import CharacterController
from .controllers.book_controller import BookController
from .dialogs.login_dialog import show_login_dialog
from application.dto.character_dto import CharacterDTO
from application.dto.book_dto import BookDTO
from utils.pinyin_helper import PinyinHelper


class LoadBooksThread(QThread):
    """加载书籍数据的后台线程"""

    books_loaded = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self, controller: BookController):
        super().__init__()
        self.controller = controller

    def run(self):
        """运行线程"""
        try:
            books = self.controller.get_all_books()
            self.books_loaded.emit(books)
        except Exception as e:
            self.error_occurred.emit(f"加载书籍时发生错误: {str(e)}")


class LoadCVsThread(QThread):
    """加载CV数据的后台线程"""

    cvs_loaded = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self, book_id: str):
        super().__init__()
        self.book_id = book_id

    def run(self):
        """运行线程"""
        try:
            from app.container import container
            from infrastructure.api.gstudios_api_service import GStudiosAPIService

            # 获取API服务
            api_service = container.get(GStudiosAPIService)

            # 获取CV列表
            success, cvs = api_service.get_cvs(self.book_id, "human")

            if success:
                self.cvs_loaded.emit(cvs)
            else:
                self.error_occurred.emit("获取CV列表失败")
        except Exception as e:
            self.error_occurred.emit(f"加载CV时发生错误: {str(e)}")


class LoadCharactersThread(QThread):
    """加载角色数据的后台线程"""

    characters_loaded = pyqtSignal(list)
    error_occurred = pyqtSignal(str)

    def __init__(self, controller: CharacterController, book_id: str):
        super().__init__()
        self.controller = controller
        self.book_id = book_id

    def run(self):
        """运行线程"""
        try:
            response = self.controller.get_characters(self.book_id)
            if response.success:
                self.characters_loaded.emit(response.characters)
            else:
                self.error_occurred.emit(response.error_message or "获取角色失败")
        except Exception as e:
            self.error_occurred.emit(f"加载角色时发生错误: {str(e)}")


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.character_controller = CharacterController()
        self.book_controller = BookController()
        self.characters: List[CharacterDTO] = []
        self.books: List[BookDTO] = []
        self.cvs: List[dict] = []  # 存储CV列表
        self.current_book_id: Optional[str] = None  # 当前选中的书籍ID
        self.load_characters_thread: Optional[LoadCharactersThread] = None
        self.load_books_thread: Optional[LoadBooksThread] = None
        self.load_cvs_thread: Optional[LoadCVsThread] = None
        self.api_token = None

        # 筛选相关属性
        self.all_characters: List[CharacterDTO] = []  # 存储所有角色（用于筛选）
        self.all_cvs: List[dict] = []  # 存储所有CV（用于筛选）

        self.init_ui()
        self.setup_connections()

        # 不在初始化时显示窗口，等待登录完成
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("CV分配工具 - 独立分离架构版本")
        self.setGeometry(100, 100, 1200, 800)

        # 创建菜单栏
        self.create_menu_bar()

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建顶部控制面板
        self.create_control_panel(main_layout)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建角色列表
        self.create_character_list(splitter)
        
        # 创建CV分配面板
        self.create_assignment_panel(splitter)
        
        # 设置分割器比例
        splitter.setSizes([600, 400])
        
        # 创建状态栏
        self.create_status_bar()
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f5f5;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                text-align: center;
                font-size: 14px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 重新登录
        relogin_action = QAction('重新登录(&L)', self)
        relogin_action.setShortcut('Ctrl+L')
        relogin_action.setStatusTip('重新进行API认证')
        relogin_action.triggered.connect(self.relogin)
        file_menu.addAction(relogin_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.setStatusTip('退出应用程序')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')

        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.setStatusTip('关于CV分配工具')
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_control_panel(self, parent_layout):
        """创建控制面板"""
        control_group = QGroupBox("控制面板")
        control_layout = QVBoxLayout(control_group)  # 改为垂直布局

        # 第一行：书籍选择
        book_row_layout = QHBoxLayout()
        book_row_layout.addWidget(QLabel("选择书籍:"))
        self.book_combo = QComboBox()
        self.book_combo.addItem("正在加载书籍...")
        self.book_combo.setEnabled(False)
        self.book_combo.setMinimumWidth(300)  # 设置最小宽度
        book_row_layout.addWidget(self.book_combo)
        book_row_layout.addStretch()
        control_layout.addLayout(book_row_layout)

        # 第二行：分类和操作
        action_row_layout = QHBoxLayout()

        # 书籍分类选择
        action_row_layout.addWidget(QLabel("书籍分类:"))
        self.category_combo = QComboBox()
        self.category_combo.addItems(["全部书籍", "进行中", "已完成"])
        self.category_combo.currentTextChanged.connect(self.on_category_changed)
        action_row_layout.addWidget(self.category_combo)

        action_row_layout.addSpacing(20)  # 添加间距

        # 加载按钮
        self.load_btn = QPushButton("加载角色")
        action_row_layout.addWidget(self.load_btn)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        action_row_layout.addWidget(self.progress_bar)

        action_row_layout.addStretch()
        control_layout.addLayout(action_row_layout)

        parent_layout.addWidget(control_group)
    
    def create_character_list(self, parent):
        """创建角色列表"""
        character_group = QGroupBox("角色列表")
        character_layout = QVBoxLayout(character_group)

        # 创建角色筛选器
        self.create_character_filter(character_layout)

        # 创建表格
        self.character_table = QTableWidget()
        self.character_table.setColumnCount(3)  # 减少到3列，移除角色ID
        self.character_table.setHorizontalHeaderLabels(["角色名称", "当前CV", "状态"])

        # 设置表格属性
        self.character_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.character_table.setAlternatingRowColors(True)
        header = self.character_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Interactive)  # 角色名称列可调整
        header.setSectionResizeMode(1, QHeaderView.Stretch)     # CV列自适应
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # 状态列适应内容

        # 设置角色名称列的初始宽度
        self.character_table.setColumnWidth(0, 150)

        character_layout.addWidget(self.character_table)
        parent.addWidget(character_group)

    def create_character_filter(self, parent_layout):
        """创建角色筛选器"""
        filter_group = QGroupBox("角色筛选")
        filter_layout = QVBoxLayout(filter_group)

        # 创建文本输入筛选区域
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("输入拼音首字母:"))

        self.character_pinyin_input = QLineEdit()
        self.character_pinyin_input.setPlaceholderText("如: zs (张三), lm (李明)")
        self.character_pinyin_input.setMaxLength(20)  # 限制输入长度
        self.character_pinyin_input.textChanged.connect(self.on_character_pinyin_input_changed)
        input_layout.addWidget(self.character_pinyin_input)

        # 清空按钮
        clear_btn = QPushButton("清空")
        clear_btn.setMaximumWidth(50)
        clear_btn.clicked.connect(self.clear_character_pinyin_input)
        input_layout.addWidget(clear_btn)

        filter_layout.addLayout(input_layout)
        parent_layout.addWidget(filter_group)

    def create_cv_filter(self, parent_layout):
        """创建CV筛选器"""
        cv_filter_group = QGroupBox("CV筛选")
        cv_filter_layout = QVBoxLayout(cv_filter_group)

        # 创建文本输入筛选区域
        cv_input_layout = QHBoxLayout()
        cv_input_layout.addWidget(QLabel("输入拼音首字母:"))

        self.cv_pinyin_input = QLineEdit()
        self.cv_pinyin_input.setPlaceholderText("如: zs (张三), lm (李明)")
        self.cv_pinyin_input.setMaxLength(20)  # 限制输入长度
        self.cv_pinyin_input.textChanged.connect(self.on_cv_pinyin_input_changed)
        cv_input_layout.addWidget(self.cv_pinyin_input)

        # 清空按钮
        cv_clear_btn = QPushButton("清空")
        cv_clear_btn.setMaximumWidth(50)
        cv_clear_btn.clicked.connect(self.clear_cv_pinyin_input)
        cv_input_layout.addWidget(cv_clear_btn)

        cv_filter_layout.addLayout(cv_input_layout)
        parent_layout.addWidget(cv_filter_group)



    def create_assignment_panel(self, parent):
        """创建分配面板"""
        assignment_group = QGroupBox("CV分配")
        assignment_layout = QVBoxLayout(assignment_group)
        
        # 选中角色信息
        self.selected_character_label = QLabel("请选择一个角色")
        self.selected_character_label.setFont(QFont("Arial", 12, QFont.Bold))
        assignment_layout.addWidget(self.selected_character_label)
        
        # CV筛选器
        self.create_cv_filter(assignment_layout)

        # CV选择
        cv_layout = QHBoxLayout()
        cv_layout.addWidget(QLabel("选择CV:"))
        self.cv_combo = AntiFlickerComboBox()  # 使用防闪烁版本
        self.cv_combo.addItem("请先选择书籍...")
        self.cv_combo.setEnabled(False)
        cv_layout.addWidget(self.cv_combo)

        # 添加CV搜索提示
        cv_hint_label = QLabel("💡 支持拼音搜索，如输入 'zs' 查找 '张三'")
        cv_hint_label.setStyleSheet("color: #666; font-size: 11px;")
        cv_layout.addWidget(cv_hint_label)

        assignment_layout.addLayout(cv_layout)
        
        # 分配按钮
        self.assign_btn = QPushButton("分配CV")
        self.assign_btn.setEnabled(False)
        assignment_layout.addWidget(self.assign_btn)
        
        # 取消分配按钮
        self.unassign_btn = QPushButton("取消分配")
        self.unassign_btn.setEnabled(False)
        self.unassign_btn.setStyleSheet("QPushButton { background-color: #f44336; }")
        assignment_layout.addWidget(self.unassign_btn)
        
        assignment_layout.addStretch()
        parent.addWidget(assignment_group)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
    
    def setup_connections(self):
        """设置信号连接"""
        self.load_btn.clicked.connect(self.load_characters)
        self.character_table.itemSelectionChanged.connect(self.on_character_selected)
        self.assign_btn.clicked.connect(self.assign_cv)
        self.unassign_btn.clicked.connect(self.unassign_cv)

    def try_auto_login(self) -> bool:
        """尝试自动登录

        Returns:
            bool: 自动登录是否成功
        """
        try:
            from app.container import container
            from infrastructure.persistence.json_config_repository import JsonConfigRepository

            # 获取配置仓储
            config_repo = container.get(JsonConfigRepository)
            saved_token = config_repo.get('API', 'default_token')

            if saved_token:
                print(f"🔍 发现保存的令牌，尝试验证...")
                # 验证保存的令牌
                if self.verify_token(saved_token):
                    print(f"✅ 令牌验证成功，自动登录")
                    self.api_token = saved_token
                    self.set_api_token(saved_token)
                    self.status_bar.showMessage("已连接到GStudios API (自动登录)")
                    self.setWindowTitle("CV分配工具 - 独立分离架构版本 (已连接)")
                    # 自动登录成功后加载书籍
                    self.load_books_on_startup()
                    return True
                else:
                    print(f"❌ 令牌验证失败，清除无效令牌")
                    # 令牌无效，清除保存的令牌
                    config_repo.set('API', 'default_token', None)
                    config_repo.save()

            # 没有保存的令牌或验证失败
            print(f"🔐 需要显示登录对话框")
            return False

        except Exception as e:
            print(f"⚠️ 自动登录过程中发生错误: {e}")
            # 出错时需要显示登录对话框
            return False

    def verify_token(self, token: str) -> bool:
        """验证API令牌是否有效

        Args:
            token: 要验证的令牌

        Returns:
            令牌是否有效
        """
        try:
            from app.container import container
            from infrastructure.api.gstudios_api_service import GStudiosAPIService

            # 获取API服务
            api_service = container.get(GStudiosAPIService)

            # 临时设置令牌进行验证
            original_token = api_service._client._config.token if hasattr(api_service, '_client') else None
            api_service.set_token(token)

            # 尝试获取书籍列表来验证令牌
            from application.book_application_service import BookApplicationService
            book_service = container.get(BookApplicationService)
            response = book_service.get_books("all")

            # 恢复原始令牌
            if original_token:
                api_service.set_token(original_token)

            return response.success

        except Exception as e:
            print(f"⚠️ 令牌验证时发生错误: {e}")
            return False



    def set_api_token(self, token: str):
        """设置API令牌"""
        try:
            from app.container import container
            from infrastructure.api.gstudios_api_service import GStudiosAPIService
            from infrastructure.persistence.json_config_repository import JsonConfigRepository

            print(f"🔧 设置API令牌: {token[:8]}...")

            # 获取API服务并设置令牌
            api_service = container.get(GStudiosAPIService)
            api_service.set_token(token)

            # 验证令牌是否正确设置
            current_token = getattr(api_service._client._config, 'token', None)
            print(f"🔍 API客户端当前令牌: {current_token[:8] if current_token else 'None'}...")

            if current_token != token:
                print(f"⚠️ 警告: 令牌设置不一致!")
                print(f"   期望: {token[:8]}...")
                print(f"   实际: {current_token[:8] if current_token else 'None'}...")

            # 保存令牌到配置文件
            config_repo = container.get(JsonConfigRepository)

            # 检查配置文件中的当前令牌
            old_token = config_repo.get('API', 'default_token')
            print(f"🔍 配置文件中的旧令牌: {old_token[:8] if old_token else 'None'}...")

            config_repo.set('API', 'default_token', token)
            config_repo.save()

            # 验证保存是否成功
            saved_token = config_repo.get('API', 'default_token')
            print(f"🔍 配置文件中的新令牌: {saved_token[:8] if saved_token else 'None'}...")

            if saved_token != token:
                print(f"⚠️ 警告: 配置文件保存不一致!")
                print(f"   期望: {token[:8]}...")
                print(f"   实际: {saved_token[:8] if saved_token else 'None'}...")

            print(f"✅ API令牌设置完成")

        except Exception as e:
            print(f"❌ 设置API令牌时发生错误: {e}")
            QMessageBox.warning(self, "警告", f"设置API令牌时发生错误: {str(e)}")

    def load_books_on_startup(self):
        """启动时加载书籍列表"""
        self.status_bar.showMessage("正在加载书籍列表...")

        # 启动后台线程加载书籍
        self.load_books_thread = LoadBooksThread(self.book_controller)
        self.load_books_thread.books_loaded.connect(self.on_books_loaded)
        self.load_books_thread.error_occurred.connect(self.on_books_load_error)
        self.load_books_thread.start()

    def on_books_loaded(self, books: List[BookDTO]):
        """书籍加载完成"""
        self.books = books
        self.update_book_combo()
        self.status_bar.showMessage(f"已加载 {len(books)} 本书籍")

    def on_books_load_error(self, error_message: str):
        """书籍加载错误处理"""
        self.book_combo.clear()
        self.book_combo.addItem("加载书籍失败")
        self.book_combo.setEnabled(False)
        self.status_bar.showMessage("书籍加载失败")
        QMessageBox.warning(self, "警告", f"加载书籍失败:\n{error_message}")

    def update_book_combo(self):
        """更新书籍下拉框"""
        self.book_combo.clear()
        self.book_combo.addItem("请选择书籍...")

        # 根据选择的分类过滤书籍
        category = self.category_combo.currentText()
        filtered_books = self.filter_books_by_category(category)

        for book in filtered_books:
            # 只显示书籍名称，不显示分类（因为已有分类选择器）
            self.book_combo.addItem(book.name)
            # 存储书籍ID作为数据
            self.book_combo.setItemData(self.book_combo.count() - 1, book.id)

        self.book_combo.setEnabled(True)

        # 更新状态栏显示当前分类的书籍数量
        count = len(filtered_books)
        category_text = "全部" if category == "全部书籍" else category
        self.status_bar.showMessage(f"已加载 {count} 本{category_text}书籍")

    def filter_books_by_category(self, category: str):
        """根据分类过滤书籍"""
        if category == "全部书籍":
            return self.books
        elif category == "进行中":
            return [book for book in self.books if not book.finished]
        elif category == "已完成":
            return [book for book in self.books if book.finished]
        else:
            return self.books

    def on_category_changed(self):
        """分类选择变化处理"""
        if self.books:  # 只有在书籍已加载时才更新
            self.update_book_combo()
            # 清空角色列表，因为分类变化了
            self.characters.clear()
            self.character_table.setRowCount(0)
            self.selected_character_label.setText("请选择一个角色")
    
    def load_characters(self):
        """加载角色数据"""
        current_index = self.book_combo.currentIndex()
        if current_index <= 0:  # 0是"请选择书籍..."
            QMessageBox.warning(self, "警告", "请先选择一个书籍")
            return

        # 获取选中书籍的ID
        book_id = self.book_combo.itemData(current_index)
        if not book_id:
            QMessageBox.warning(self, "警告", "无效的书籍选择")
            return

        self.current_book_id = book_id  # 保存当前书籍ID

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        self.load_btn.setEnabled(False)
        self.status_bar.showMessage("正在加载角色和CV数据...")

        # 启动角色加载线程
        self.load_characters_thread = LoadCharactersThread(self.character_controller, book_id)
        self.load_characters_thread.characters_loaded.connect(self.on_characters_loaded)
        self.load_characters_thread.error_occurred.connect(self.on_load_error)
        self.load_characters_thread.start()

        # 启动CV加载线程
        self.load_cvs_thread = LoadCVsThread(book_id)
        self.load_cvs_thread.cvs_loaded.connect(self.on_cvs_loaded)
        self.load_cvs_thread.error_occurred.connect(self.on_cv_load_error)
        self.load_cvs_thread.start()
    
    def on_characters_loaded(self, characters: List[CharacterDTO]):
        """角色加载完成"""
        self.all_characters = characters  # 保存所有角色
        self.characters = characters  # 当前显示的角色
        self.update_character_table()

        # 重置角色筛选器
        self.reset_character_filter()

        # 隐藏进度条
        self.progress_bar.setVisible(False)
        self.load_btn.setEnabled(True)
        self.status_bar.showMessage(f"已加载 {len(characters)} 个角色")
    
    def on_load_error(self, error_message: str):
        """加载错误处理"""
        self.progress_bar.setVisible(False)
        self.load_btn.setEnabled(True)
        self.status_bar.showMessage("加载失败")
        QMessageBox.critical(self, "错误", f"加载角色失败:\n{error_message}")

    def on_cvs_loaded(self, cvs: list):
        """CV加载完成"""
        self.all_cvs = cvs  # 保存所有CV
        self.cvs = cvs  # 当前显示的CV
        print(f"✅ 已加载 {len(cvs)} 个CV")

        # 调试：打印CV数据结构
        for i, cv in enumerate(cvs[:3]):  # 只打印前3个CV的详细信息
            print(f"CV {i+1}: {cv}")

        # 重置CV筛选器
        self.reset_cv_filter()

        self.update_cv_combo()

    def on_cv_load_error(self, error_message: str):
        """CV加载错误处理"""
        print(f"⚠️ CV加载失败: {error_message}")
        # CV加载失败不影响角色显示，只是CV选择不可用
        self.cv_combo.clear()
        self.cv_combo.addItem("CV加载失败")
        self.cv_combo.setEnabled(False)

    def update_cv_combo(self):
        """更新CV下拉框"""
        print(f"🔄 开始更新CV下拉框，CV数量: {len(self.cvs) if self.cvs else 0}")

        self.cv_combo.clear()
        self.cv_combo.addItem("请选择CV...")

        if self.cvs:
            for i, cv in enumerate(self.cvs):
                # 兼容不同的API数据结构
                # 模拟数据使用: id, name
                # 真实API使用: cvId, cvName
                cv_name = (cv.get('name') or cv.get('cvName') or
                          cv.get('id') or str(cv.get('cvId')) or '未知CV')
                cv_id = cv.get('id') or str(cv.get('cvId')) or None

                print(f"   添加CV {i+1}: '{cv_name}' (ID: {cv_id})")

                # 直接在addItem时传递数据
                self.cv_combo.addItem(cv_name, cv_id)

            self.cv_combo.setEnabled(True)
            print(f"📋 CV列表已更新，共 {len(self.cvs)} 个CV")

            # 验证添加的项目
            print(f"🔍 验证: ComboBox项目数量 = {self.cv_combo.count()}")
            for i in range(min(3, self.cv_combo.count())):  # 只显示前3个
                text = self.cv_combo.itemText(i)
                data = self.cv_combo.itemData(i)
                print(f"   项目 {i}: '{text}' (数据: {data})")
        else:
            self.cv_combo.addItem("暂无可用CV")
            self.cv_combo.setEnabled(False)
            print("⚠️ 没有CV数据，显示默认提示")
    
    def update_character_table(self):
        """更新角色表格"""
        self.character_table.setRowCount(len(self.characters))

        for row, character in enumerate(self.characters):
            # 角色名称
            name_item = QTableWidgetItem(character.name)
            name_item.setToolTip(f"角色ID: {character.id}")  # 将ID作为工具提示显示
            self.character_table.setItem(row, 0, name_item)

            # 当前CV - 优先显示CV名字，如果没有则显示CV ID
            if character.cv_name:
                cv_text = character.cv_name
            elif character.cv_id:
                cv_text = character.cv_id
            else:
                cv_text = "未分配"
            cv_item = QTableWidgetItem(cv_text)
            if character.cv_id:
                cv_item.setToolTip(f"CV ID: {character.cv_id}")  # CV ID作为工具提示
            self.character_table.setItem(row, 1, cv_item)

            # 状态
            status = "已分配" if character.cv_id else "未分配"
            status_item = QTableWidgetItem(status)
            if character.cv_id:
                status_item.setBackground(QColor(144, 238, 144))  # lightGreen
            else:
                status_item.setBackground(QColor(255, 255, 224))  # lightYellow
            self.character_table.setItem(row, 2, status_item)
    
    def on_character_selected(self):
        """角色选择变化"""
        current_row = self.character_table.currentRow()
        if current_row >= 0 and current_row < len(self.characters):
            character = self.characters[current_row]
            self.selected_character_label.setText(f"选中角色: {character.name}")
            self.assign_btn.setEnabled(True)
            self.unassign_btn.setEnabled(bool(character.cv_id))
        else:
            self.selected_character_label.setText("请选择一个角色")
            self.assign_btn.setEnabled(False)
            self.unassign_btn.setEnabled(False)
    
    def assign_cv(self):
        """分配CV"""
        current_row = self.character_table.currentRow()
        if current_row < 0:
            return
        
        cv_name = self.cv_combo.currentText()
        if cv_name == "请选择CV...":
            QMessageBox.warning(self, "警告", "请选择一个CV")
            return
        
        character = self.characters[current_row]
        
        # 这里应该调用实际的分配逻辑
        # 由于当前分配功能未完全实现，我们显示一个消息
        QMessageBox.information(
            self, 
            "功能提示", 
            f"CV分配功能正在开发中\n\n"
            f"将要执行的操作:\n"
            f"角色: {character.name}\n"
            f"CV: {cv_name}\n"
            f"书籍: {self.book_combo.currentText()}"
        )
    
    def unassign_cv(self):
        """取消CV分配"""
        current_row = self.character_table.currentRow()
        if current_row < 0:
            return
        
        character = self.characters[current_row]
        
        reply = QMessageBox.question(
            self, 
            "确认", 
            f"确定要取消角色 '{character.name}' 的CV分配吗？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "功能提示", "取消CV分配功能正在开发中")

    def relogin(self):
        """重新登录"""
        reply = QMessageBox.question(
            self,
            "确认",
            "确定要重新登录吗？\n\n这将清除当前的认证状态。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 清除保存的令牌
            try:
                from app.container import container
                from infrastructure.persistence.json_config_repository import JsonConfigRepository

                config_repo = container.get(JsonConfigRepository)
                config_repo.set('API', 'default_token', None)
                config_repo.save()
                print(f"🗑️ 已清除保存的令牌")
            except Exception as e:
                print(f"⚠️ 清除令牌时发生错误: {e}")

            # 清除当前数据
            self.characters.clear()
            self.books.clear()
            self.character_table.setRowCount(0)
            self.book_combo.clear()
            self.book_combo.addItem("正在重新登录...")
            self.book_combo.setEnabled(False)

            # 显示登录对话框
            success, token = show_login_dialog(self)
            if success:
                # 登录成功（令牌已在对话框中验证），设置令牌
                self.api_token = token
                if token:  # 有令牌，使用真实API
                    self.set_api_token(token)
                    print(f"💾 令牌已保存到配置文件")
                    self.status_bar.showMessage("已连接到GStudios API")
                    self.setWindowTitle("CV分配工具 - 独立分离架构版本 (已连接)")
                else:  # 无令牌，使用模拟数据
                    self.status_bar.showMessage("使用模拟数据模式")
                    self.setWindowTitle("CV分配工具 - 独立分离架构版本 (模拟数据)")

                # 登录成功后加载书籍
                self.load_books_on_startup()
            else:
                # 用户取消重新登录，关闭应用
                self.close()

    def show_about(self):
        """显示关于对话框"""
        about_text = """
        <h2>CV分配工具</h2>
        <p><b>版本:</b> 2.0.0 (独立分离架构版本)</p>
        <p><b>架构:</b> 清洁架构 + 领域驱动设计</p>
        <p><b>技术栈:</b> Python 3.8+, PyQt5, GStudios API</p>
        <br>
        <p>这是一个用于管理角色CV分配的专业工具，</p>
        <p>采用现代化的软件架构设计，提供友好的图形界面。</p>
        <br>
        <p><b>功能特性:</b></p>
        <ul>
        <li>书籍和角色数据管理</li>
        <li>CV分配和管理</li>
        <li>模拟数据支持</li>
        <li>实时数据同步</li>
        </ul>
        """

        QMessageBox.about(self, "关于CV分配工具", about_text)



    def on_character_pinyin_input_changed(self):
        """拼音输入框内容变化处理"""
        input_text = self.character_pinyin_input.text().strip()

        if not input_text:
            # 输入为空时显示全部
            self.characters = self.all_characters.copy()
            self.status_bar.showMessage(f"显示全部 {len(self.characters)} 个角色")
        else:
            # 根据输入的拼音首字母筛选
            self.characters = PinyinHelper.filter_by_pinyin_input(
                self.all_characters,
                input_text,
                key_func=lambda char: char.name
            )

            # 更新状态栏
            total = len(self.all_characters)
            filtered = len(self.characters)
            if filtered == 0:
                self.status_bar.showMessage(f"没有找到拼音首字母包含 '{input_text.upper()}' 的角色")
            else:
                self.status_bar.showMessage(f"显示拼音首字母包含 '{input_text.upper()}' 的角色: {filtered}/{total}")

        self.update_character_table()

    def clear_character_pinyin_input(self):
        """清空拼音输入框"""
        self.character_pinyin_input.clear()

    def on_cv_pinyin_input_changed(self):
        """CV拼音输入框内容变化处理"""
        input_text = self.cv_pinyin_input.text().strip()

        if not input_text:
            # 输入为空时显示全部
            self.cvs = self.all_cvs.copy()
        else:
            # 根据输入的拼音首字母筛选
            self.cvs = PinyinHelper.filter_by_pinyin_input(
                self.all_cvs,
                input_text,
                key_func=lambda cv: cv.get('name') or cv.get('cvName') or str(cv.get('id', ''))
            )

        self.update_cv_combo()

    def clear_cv_pinyin_input(self):
        """清空CV拼音输入框"""
        self.cv_pinyin_input.clear()



    def reset_character_filter(self):
        """重置角色筛选器"""
        # 清空文本输入框
        if hasattr(self, 'character_pinyin_input'):
            self.character_pinyin_input.blockSignals(True)
            self.character_pinyin_input.clear()
            self.character_pinyin_input.blockSignals(False)

    def reset_cv_filter(self):
        """重置CV筛选器"""
        # 清空文本输入框
        if hasattr(self, 'cv_pinyin_input'):
            self.cv_pinyin_input.blockSignals(True)
            self.cv_pinyin_input.clear()
            self.cv_pinyin_input.blockSignals(False)


def create_application():
    """创建并返回QApplication实例"""
    app = QApplication(sys.argv)
    app.setApplicationName("CV分配工具")
    app.setApplicationVersion("2.0.0")
    return app


def run_gui():
    """运行GUI应用"""
    app = create_application()

    # 创建主窗口（但不显示）
    window = MainWindow()

    # 尝试自动登录，如果失败则显示登录对话框
    if window.try_auto_login():
        # 自动登录成功，显示主窗口
        window.show()
    else:
        # 自动登录失败，显示登录对话框
        success, token = show_login_dialog(None)
        if success:
            # 登录成功（令牌已在对话框中验证），设置令牌并显示主窗口
            window.api_token = token
            if token:  # 有令牌，使用真实API
                window.set_api_token(token)
                print(f"💾 令牌已保存到配置文件")
                window.status_bar.showMessage("已连接到GStudios API")
                window.setWindowTitle("CV分配工具 - 独立分离架构版本 (已连接)")
            else:  # 无令牌，使用模拟数据
                window.status_bar.showMessage("使用模拟数据模式")
                window.setWindowTitle("CV分配工具 - 独立分离架构版本 (模拟数据)")

            # 登录成功后加载书籍并显示窗口
            window.load_books_on_startup()
            window.show()
        else:
            # 用户取消登录，直接退出
            return 0

    # 运行应用
    return app.exec_()
