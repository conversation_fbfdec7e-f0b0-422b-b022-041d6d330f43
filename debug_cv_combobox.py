#!/usr/bin/env python3
"""CV下拉框问题诊断脚本

此脚本用于诊断CV下拉框不显示CV名字的问题。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import Qt
from presentation.gui.searchable_combobox import SearchableComboBox


class CVComboBoxDebugWindow(QMainWindow):
    """CV下拉框调试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.test_searchable_combobox()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("CV下拉框问题诊断")
        self.setGeometry(100, 100, 600, 500)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("CV下拉框问题诊断")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 测试说明
        info_label = QLabel(
            "🔍 诊断内容:\n"
            "• 测试SearchableComboBox基本功能\n"
            "• 验证addItem()方法是否正常工作\n"
            "• 检查数据存储和显示机制\n"
            "• 模拟CV数据加载过程"
        )
        info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # 原始ComboBox测试
        layout.addWidget(QLabel("1. 原始QComboBox测试:"))
        from PyQt5.QtWidgets import QComboBox
        self.original_combo = QComboBox()
        layout.addWidget(self.original_combo)
        
        # SearchableComboBox测试
        layout.addWidget(QLabel("2. SearchableComboBox测试:"))
        self.searchable_combo = SearchableComboBox()
        layout.addWidget(self.searchable_combo)
        
        # 状态显示
        self.status_label = QLabel("状态: 准备测试")
        self.status_label.setStyleSheet("margin: 10px; padding: 5px; background: #e8f4fd; border-radius: 3px;")
        layout.addWidget(self.status_label)
        
        # 测试按钮
        test_button = QPushButton("开始测试")
        test_button.clicked.connect(self.run_tests)
        layout.addWidget(test_button)
        
        # 清空按钮
        clear_button = QPushButton("清空测试")
        clear_button.clicked.connect(self.clear_tests)
        layout.addWidget(clear_button)
        
        layout.addStretch()
    
    def test_searchable_combobox(self):
        """测试SearchableComboBox基本功能"""
        print("🔍 测试SearchableComboBox基本功能")
        
        try:
            # 创建测试实例
            combo = SearchableComboBox()
            print("✅ SearchableComboBox创建成功")
            
            # 测试addItem方法
            combo.addItem("测试项目1", "data1")
            combo.addItem("测试项目2", "data2")
            print(f"✅ addItem方法正常，项目数量: {combo.count()}")
            
            # 测试数据获取
            for i in range(combo.count()):
                text = combo.itemText(i)
                data = combo.itemData(i)
                print(f"   项目 {i}: '{text}' (数据: {data})")
            
            return True
            
        except Exception as e:
            print(f"❌ SearchableComboBox测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_tests(self):
        """运行所有测试"""
        print("\n" + "="*50)
        print("🚀 开始CV下拉框诊断测试")
        print("="*50)
        
        # 测试1: 原始ComboBox
        self.test_original_combobox()
        
        # 测试2: SearchableComboBox
        self.test_searchable_combobox_detailed()
        
        # 测试3: 模拟CV数据
        self.test_cv_data_simulation()
        
        # 测试4: 数据结构兼容性
        self.test_data_structure_compatibility()
        
        self.status_label.setText("状态: 测试完成，请查看控制台输出")
    
    def test_original_combobox(self):
        """测试原始ComboBox"""
        print("\n📝 测试1: 原始QComboBox")
        
        try:
            self.original_combo.clear()
            self.original_combo.addItem("请选择CV...")
            
            # 添加测试CV数据
            test_cvs = ["张三", "李明轩", "王诗涵", "陈奕迅"]
            for cv_name in test_cvs:
                self.original_combo.addItem(cv_name)
            
            print(f"✅ 原始ComboBox正常，项目数量: {self.original_combo.count()}")
            print(f"   项目列表: {[self.original_combo.itemText(i) for i in range(self.original_combo.count())]}")
            
        except Exception as e:
            print(f"❌ 原始ComboBox测试失败: {e}")
    
    def test_searchable_combobox_detailed(self):
        """详细测试SearchableComboBox"""
        print("\n📝 测试2: SearchableComboBox详细测试")
        
        try:
            self.searchable_combo.clear()
            self.searchable_combo.addItem("请选择CV...")
            
            # 添加测试CV数据
            test_cvs = [
                {"name": "张三", "id": "cv_001"},
                {"name": "李明轩", "id": "cv_002"},
                {"name": "王诗涵", "id": "cv_003"},
                {"name": "陈奕迅", "id": "cv_004"},
            ]
            
            for cv in test_cvs:
                self.searchable_combo.addItem(cv["name"], cv["id"])
            
            print(f"✅ SearchableComboBox正常，项目数量: {self.searchable_combo.count()}")
            
            # 检查内部数据
            print(f"   原始项目列表: {self.searchable_combo.original_items}")
            print(f"   原始数据列表: {self.searchable_combo.original_data}")
            print(f"   模型行数: {self.searchable_combo.model.rowCount()}")
            
            # 检查模型中的数据
            for i in range(self.searchable_combo.model.rowCount()):
                item = self.searchable_combo.model.item(i)
                if item:
                    text = item.text()
                    data = item.data(Qt.UserRole)
                    print(f"   模型项目 {i}: '{text}' (数据: {data})")
            
        except Exception as e:
            print(f"❌ SearchableComboBox详细测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    def test_cv_data_simulation(self):
        """模拟CV数据加载测试"""
        print("\n📝 测试3: 模拟CV数据加载")
        
        try:
            # 模拟不同的CV数据结构
            test_data_sets = [
                # 模拟数据格式
                [
                    {"id": "cv_001", "name": "张三"},
                    {"id": "cv_002", "name": "李明轩"},
                ],
                # 真实API格式
                [
                    {"cvId": "cv_003", "cvName": "王诗涵"},
                    {"cvId": "cv_004", "cvName": "陈奕迅"},
                ],
                # 混合格式
                [
                    {"id": "cv_005", "name": "小红"},
                    {"cvId": "cv_006", "cvName": "赵钱孙"},
                ],
                # 缺失字段格式
                [
                    {"id": "cv_007"},  # 只有ID
                    {"cvName": "周杰伦"},  # 只有名字
                    {},  # 空对象
                ]
            ]
            
            for i, data_set in enumerate(test_data_sets, 1):
                print(f"\n   数据集 {i}: {data_set}")
                
                # 模拟update_cv_combo的逻辑
                processed_cvs = []
                for cv in data_set:
                    cv_name = (cv.get('name') or cv.get('cvName') or
                              cv.get('id') or str(cv.get('cvId')) or '未知CV')
                    cv_id = cv.get('id') or str(cv.get('cvId')) or None
                    processed_cvs.append({"name": cv_name, "id": cv_id})
                
                print(f"   处理后: {processed_cvs}")
            
            print("✅ CV数据模拟测试完成")
            
        except Exception as e:
            print(f"❌ CV数据模拟测试失败: {e}")
    
    def test_data_structure_compatibility(self):
        """测试数据结构兼容性"""
        print("\n📝 测试4: 数据结构兼容性")
        
        try:
            # 测试主窗口的CV处理逻辑
            test_cvs = [
                {"id": "cv_001", "name": "张三"},
                {"cvId": "cv_002", "cvName": "李明轩"},
                {"id": "cv_003"},  # 只有ID
                {"cvName": "王诗涵"},  # 只有名字
                {},  # 空对象
            ]
            
            print("   测试CV数据处理逻辑:")
            for i, cv in enumerate(test_cvs):
                # 模拟主窗口的处理逻辑
                cv_name = (cv.get('name') or cv.get('cvName') or
                          cv.get('id') or str(cv.get('cvId')) or '未知CV')
                cv_id = cv.get('id') or str(cv.get('cvId')) or None
                
                print(f"   CV {i+1}: {cv} → 名称: '{cv_name}', ID: {cv_id}")
            
            print("✅ 数据结构兼容性测试完成")
            
        except Exception as e:
            print(f"❌ 数据结构兼容性测试失败: {e}")
    
    def clear_tests(self):
        """清空测试"""
        self.original_combo.clear()
        self.searchable_combo.clear()
        self.status_label.setText("状态: 已清空测试")
        print("\n🧹 测试已清空")


def main():
    """主函数"""
    print("🎯 CV下拉框问题诊断工具")
    print("="*50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建调试窗口
    window = CVComboBoxDebugWindow()
    window.show()
    
    print("✅ 诊断窗口已启动")
    print("\n💡 使用说明:")
    print("  1. 点击'开始测试'按钮运行所有诊断测试")
    print("  2. 查看控制台输出了解详细测试结果")
    print("  3. 观察两个下拉框的显示差异")
    print("  4. 测试SearchableComboBox的搜索功能")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
