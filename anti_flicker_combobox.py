#!/usr/bin/env python3
"""防闪烁的SearchableComboBox

专门解决下拉框闪烁问题的版本。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QComboBox
from PyQt5.QtCore import Qt, QTimer
from utils.pinyin_helper import PinyinHelper


class AntiFlickerComboBox(QComboBox):
    """防闪烁的可搜索ComboBox"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 存储所有项目数据
        self.all_items = []  # [{'text': str, 'data': any}, ...]
        self.filtered_items = []  # 当前筛选后的项目
        self.current_search = ""
        
        # 防闪烁控制
        self.is_updating = False
        self.update_pending = False
        
        self.setup_search_functionality()
    
    def setup_search_functionality(self):
        """设置搜索功能"""
        # 设置为可编辑
        self.setEditable(True)
        self.setInsertPolicy(QComboBox.NoInsert)
        
        # 连接编辑信号
        self.lineEdit().textChanged.connect(self.on_text_changed)
        
        # 设置搜索定时器
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
    
    def on_text_changed(self, text):
        """文本变化处理"""
        self.current_search = text.strip()
        
        # 如果正在更新，标记为待更新
        if self.is_updating:
            self.update_pending = True
            return
        
        # 使用定时器延迟搜索
        self.search_timer.stop()
        self.search_timer.start(300)
    
    def perform_search(self):
        """执行搜索"""
        if self.is_updating:
            self.update_pending = True
            return
        
        search_text = self.current_search
        
        if not search_text:
            # 如果搜索为空，显示所有项目
            self.filtered_items = self.all_items.copy()
        else:
            # 筛选匹配的项目
            self.filtered_items = []
            for item_info in self.all_items:
                text = item_info['text']
                
                # 跳过默认提示项
                if text in ["请选择CV...", "请先选择书籍...", "CV加载失败", "暂无可用CV"]:
                    self.filtered_items.append(item_info)
                    continue
                
                # 使用拼音助手进行匹配
                if self.matches_search(text, search_text):
                    self.filtered_items.append(item_info)
        
        # 更新显示
        self.update_display()
    
    def matches_search(self, text, search_text):
        """检查文本是否匹配搜索"""
        if not search_text:
            return True
        
        # 使用拼音助手进行匹配
        item_pinyin = PinyinHelper.get_text_pinyin_letters(text)
        search_upper = search_text.upper()
        
        # 支持多种匹配方式
        return (search_upper in item_pinyin or                    # 拼音首字母匹配
                search_upper in text.upper() or                   # 直接文本匹配
                item_pinyin.startswith(search_upper))              # 拼音开头匹配
    
    def update_display(self):
        """更新显示内容"""
        if self.is_updating:
            self.update_pending = True
            return
        
        self.is_updating = True
        
        try:
            # 保存当前状态
            current_text = self.lineEdit().text()
            was_popup_visible = self.view().isVisible()
            
            # 暂时禁用信号和重绘
            self.blockSignals(True)
            self.setUpdatesEnabled(False)
            
            # 清空并重新添加项目
            super().clear()
            
            for item_info in self.filtered_items:
                super().addItem(item_info['text'])
                if item_info['data'] is not None:
                    super().setItemData(super().count() - 1, item_info['data'], Qt.UserRole)
            
            # 恢复输入文本
            self.lineEdit().setText(current_text)
            
            # 重新启用信号和重绘
            self.setUpdatesEnabled(True)
            self.blockSignals(False)
            
            # 如果之前弹出框是可见的且有结果，重新显示
            if was_popup_visible and self.filtered_items and self.current_search:
                QTimer.singleShot(50, self.showPopup)  # 延迟显示，避免闪烁
            
        finally:
            self.is_updating = False
            
            # 如果有待处理的更新，执行它
            if self.update_pending:
                self.update_pending = False
                QTimer.singleShot(100, self.perform_search)
    
    def addItem(self, text, userData=None):
        """添加项目"""
        item_info = {'text': text, 'data': userData}
        self.all_items.append(item_info)
        
        # 如果当前没有搜索，直接添加到显示
        if not self.current_search:
            self.filtered_items.append(item_info)
            super().addItem(text)
            if userData is not None:
                super().setItemData(super().count() - 1, userData, Qt.UserRole)
    
    def clear(self):
        """清空所有项目"""
        self.all_items.clear()
        self.filtered_items.clear()
        self.current_search = ""
        self.is_updating = False
        self.update_pending = False
        super().clear()
    
    def mousePressEvent(self, event):
        """鼠标按下事件处理"""
        # 如果点击下拉按钮且有搜索，先恢复所有项目
        if self.current_search and not self.is_updating:
            self.current_search = ""
            self.lineEdit().clear()
            self.filtered_items = self.all_items.copy()
            self.update_display()
        
        super().mousePressEvent(event)
    
    def keyPressEvent(self, event):
        """键盘事件处理"""
        # 如果按下Escape键，清空搜索
        if event.key() == Qt.Key_Escape:
            self.lineEdit().clear()
            self.current_search = ""
            self.filtered_items = self.all_items.copy()
            self.update_display()
            self.hidePopup()
            return
        
        super().keyPressEvent(event)
    
    def count(self):
        """获取项目数量"""
        return len(self.all_items)
    
    def itemText(self, index):
        """获取指定索引的文本"""
        if 0 <= index < len(self.all_items):
            return self.all_items[index]['text']
        return ""
    
    def itemData(self, index, role=Qt.UserRole):
        """获取指定索引的数据"""
        if 0 <= index < len(self.all_items):
            return self.all_items[index]['data']
        return None


class TestAntiFlickerWindow(QMainWindow):
    """测试防闪烁ComboBox的窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_test_data()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("防闪烁ComboBox测试")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("防闪烁ComboBox测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 说明
        info_label = QLabel(
            "🔧 防闪烁特性:\n"
            "• 使用信号阻塞避免频繁更新\n"
            "• 延迟搜索减少UI刷新\n"
            "• 智能状态管理防止冲突\n"
            "• 优化的鼠标和键盘事件处理"
        )
        info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # 防闪烁ComboBox
        layout.addWidget(QLabel("防闪烁ComboBox:"))
        self.cv_combo = AntiFlickerComboBox()
        layout.addWidget(self.cv_combo)
        
        # 当前选择显示
        self.selection_label = QLabel("当前选择: 无")
        self.selection_label.setStyleSheet("margin: 10px; padding: 5px; background: #e8f4fd; border-radius: 3px;")
        layout.addWidget(self.selection_label)
        
        # 测试按钮
        test_button = QPushButton("重新加载测试数据")
        test_button.clicked.connect(self.load_test_data)
        layout.addWidget(test_button)
        
        layout.addStretch()
        
        # 连接信号
        self.cv_combo.currentTextChanged.connect(self.on_selection_changed)
    
    def load_test_data(self):
        """加载测试数据"""
        print("🔄 加载测试数据...")
        
        # 清空现有数据
        self.cv_combo.clear()
        
        # 模拟CV数据
        test_cvs = [
            {"name": "张三", "id": "cv_001"},
            {"name": "李明轩", "id": "cv_002"},
            {"name": "王诗涵", "id": "cv_003"},
            {"name": "陈奕迅", "id": "cv_004"},
            {"name": "小红", "id": "cv_005"},
            {"name": "赵钱孙", "id": "cv_006"},
            {"name": "周杰伦", "id": "cv_007"},
            {"name": "刘德华", "id": "cv_008"},
        ]
        
        # 添加默认选项
        self.cv_combo.addItem("请选择CV...")
        
        # 添加CV数据
        for cv in test_cvs:
            self.cv_combo.addItem(cv["name"], cv["id"])
        
        print(f"✅ 加载完成，总数: {self.cv_combo.count()}")
        self.selection_label.setText("当前选择: 无 (数据已重新加载)")
    
    def on_selection_changed(self, text):
        """选择变化处理"""
        if text and text != "请选择CV...":
            cv_id = self.cv_combo.currentData()
            self.selection_label.setText(f"当前选择: {text} (ID: {cv_id})")
        else:
            self.selection_label.setText("当前选择: 无")


def main():
    """主函数"""
    print("🎯 防闪烁ComboBox测试")
    print("="*50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestAntiFlickerWindow()
    window.show()
    
    print("✅ 测试窗口已启动")
    print("\n💡 测试说明:")
    print("  1. 点击下拉按钮应该不会闪烁")
    print("  2. 输入拼音首字母进行搜索 (如 'z', 'zs', 'lm')")
    print("  3. 鼠标点击时应该平滑无闪烁")
    print("  4. 按Escape键清空搜索")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
