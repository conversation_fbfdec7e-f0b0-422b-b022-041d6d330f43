#!/usr/bin/env python3
"""防闪烁的SearchableComboBox

专门解决下拉框闪烁问题的版本。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QComboBox
from PyQt5.QtCore import Qt, QTimer
from utils.pinyin_helper import PinyinHelper


class AntiFlickerComboBox(QComboBox):
    """防闪烁的可搜索ComboBox"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 存储所有项目数据
        self.all_items = []  # [{'text': str, 'data': any}, ...]
        self.filtered_items = []  # 当前筛选后的项目
        self.current_search = ""
        self.last_search_text = ""  # 记录上次搜索的文本，用于智能更新

        # 防闪烁控制
        self.is_updating = False
        self.update_pending = False

        # 焦点和下拉列表状态管理
        self.should_keep_popup_open = False
        self.is_programmatic_change = False  # 标记是否为程序化改变文本

        self.setup_search_functionality()
    
    def setup_search_functionality(self):
        """设置搜索功能"""
        # 设置为可编辑
        self.setEditable(True)
        self.setInsertPolicy(QComboBox.NoInsert)

        # 连接编辑信号 - 使用textChanged而不是textEdited，确保捕获所有文本变化
        self.lineEdit().textChanged.connect(self.on_text_changed)

        # 连接选择信号
        self.activated.connect(self.on_item_selected)

        # 连接焦点事件
        self.lineEdit().focusInEvent = self.on_focus_in
        self.lineEdit().focusOutEvent = self.on_focus_out

        # 重写输入框的键盘事件处理，确保输入始终有效
        self.lineEdit().keyPressEvent = self.on_lineedit_key_press

        # 设置搜索定时器 - 减少延迟以提供更实时的响应
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)
    
    def on_text_changed(self, text):
        """文本变化处理 - 智能更新机制"""
        # 如果是程序化改变文本，跳过处理
        if self.is_programmatic_change:
            return

        new_search_text = text.strip()

        # 智能更新：仅当文本内容实际发生变化时才触发更新
        if new_search_text == self.last_search_text:
            return

        self.current_search = new_search_text
        self.last_search_text = new_search_text

        # 如果正在更新，标记为待更新
        if self.is_updating:
            self.update_pending = True
            return

        # 立即搜索以提供实时响应，不使用延迟
        self.perform_search()

    def on_focus_in(self, event):
        """输入框获得焦点时的处理"""
        from PyQt5.QtWidgets import QLineEdit
        # 调用原始的focusInEvent
        QLineEdit.focusInEvent(self.lineEdit(), event)

        # 设置标志，表示应该保持下拉列表打开
        self.should_keep_popup_open = True

        # 自动显示下拉列表
        if self.all_items:
            # 确保显示当前筛选的项目
            if not self.current_search:
                self.filtered_items = self.all_items.copy()
                self.update_display()

            # 延迟显示下拉列表，确保更新完成
            QTimer.singleShot(50, self.show_popup_if_needed)

    def on_focus_out(self, event):
        """输入框失去焦点时的处理"""
        from PyQt5.QtWidgets import QLineEdit
        # 调用原始的focusOutEvent
        QLineEdit.focusOutEvent(self.lineEdit(), event)

        # 清除保持下拉列表打开的标志
        self.should_keep_popup_open = False

    def show_popup_if_needed(self):
        """如果需要，显示下拉列表"""
        if self.should_keep_popup_open and self.filtered_items and not self.view().isVisible():
            self.showPopup()
            # 确保焦点保持在输入框中
            self.lineEdit().setFocus()

    def show_popup_and_keep_focus(self):
        """显示下拉列表并确保焦点保持在输入框"""
        if self.filtered_items:
            self.showPopup()
            # 强制焦点回到输入框
            QTimer.singleShot(1, lambda: self.lineEdit().setFocus())

    def showPopup(self):
        """重写showPopup方法，确保焦点保持在输入框"""
        super().showPopup()
        # 确保焦点始终在输入框中
        QTimer.singleShot(1, self.ensure_focus_in_lineedit)

    def ensure_focus_in_lineedit(self):
        """确保焦点在输入框中"""
        if not self.lineEdit().hasFocus():
            self.lineEdit().setFocus()

    def on_item_selected(self, index):
        """处理项目选择事件"""
        # 选择项目后，确保焦点回到输入框
        QTimer.singleShot(1, self.ensure_focus_in_lineedit)

    def on_lineedit_key_press(self, event):
        """输入框键盘事件处理 - 确保输入始终有效"""
        from PyQt5.QtWidgets import QLineEdit

        # 如果按下Escape键，清空搜索
        if event.key() == Qt.Key_Escape:
            self.is_programmatic_change = True
            self.lineEdit().clear()
            self.is_programmatic_change = False

            self.current_search = ""
            self.last_search_text = ""
            self.filtered_items = self.all_items.copy()
            self.update_display()
            return

        # 如果按下上下箭头键，传递给ComboBox处理导航
        elif event.key() in (Qt.Key_Up, Qt.Key_Down):
            if self.view().isVisible():
                # 传递给ComboBox的键盘事件处理
                super(AntiFlickerComboBox, self).keyPressEvent(event)
                return

        # 如果按下回车键，选择当前项目
        elif event.key() in (Qt.Key_Return, Qt.Key_Enter):
            if self.view().isVisible():
                current_index = self.view().currentIndex()
                if current_index.isValid():
                    self.setCurrentIndex(current_index.row())
                    self.hidePopup()
                    return

        # 对于所有其他键盘输入（包括字符输入），使用原始的LineEdit处理
        QLineEdit.keyPressEvent(self.lineEdit(), event)
    
    def perform_search(self):
        """执行搜索"""
        if self.is_updating:
            self.update_pending = True
            return
        
        search_text = self.current_search
        
        if not search_text:
            # 如果搜索为空，显示所有项目
            self.filtered_items = self.all_items.copy()
        else:
            # 筛选匹配的项目
            self.filtered_items = []
            for item_info in self.all_items:
                text = item_info['text']
                
                # 跳过默认提示项
                if text in ["请选择CV...", "请先选择书籍...", "CV加载失败", "暂无可用CV"]:
                    self.filtered_items.append(item_info)
                    continue
                
                # 使用拼音助手进行匹配
                if self.matches_search(text, search_text):
                    self.filtered_items.append(item_info)
        
        # 更新显示
        self.update_display()
    
    def matches_search(self, text, search_text):
        """检查文本是否匹配搜索"""
        if not search_text:
            return True
        
        # 使用拼音助手进行匹配
        item_pinyin = PinyinHelper.get_text_pinyin_letters(text)
        search_upper = search_text.upper()
        
        # 支持多种匹配方式
        return (search_upper in item_pinyin or                    # 拼音首字母匹配
                search_upper in text.upper() or                   # 直接文本匹配
                item_pinyin.startswith(search_upper))              # 拼音开头匹配
    
    def update_display(self):
        """更新显示内容 - 优化版本，保持下拉列表打开"""
        if self.is_updating:
            self.update_pending = True
            return

        self.is_updating = True

        try:
            # 保存当前状态
            current_text = self.lineEdit().text()
            was_popup_visible = self.view().isVisible()
            has_focus = self.lineEdit().hasFocus()

            # 标记为程序化改变，避免触发textChanged信号处理
            self.is_programmatic_change = True

            # 暂时禁用信号和重绘
            self.blockSignals(True)
            self.setUpdatesEnabled(False)

            # 清空并重新添加项目
            super().clear()

            for item_info in self.filtered_items:
                super().addItem(item_info['text'])
                if item_info['data'] is not None:
                    super().setItemData(super().count() - 1, item_info['data'], Qt.UserRole)

            # 恢复输入文本
            self.lineEdit().setText(current_text)

            # 重新启用信号和重绘
            self.setUpdatesEnabled(True)
            self.blockSignals(False)

            # 清除程序化改变标记
            self.is_programmatic_change = False

            # 智能显示下拉列表：
            # 1. 如果输入框有焦点，保持下拉列表打开
            # 2. 如果之前下拉列表是可见的，重新显示
            # 3. 如果设置了保持打开标志，显示下拉列表
            should_show_popup = (
                (has_focus and self.should_keep_popup_open) or
                (was_popup_visible) or
                (self.should_keep_popup_open and self.filtered_items)
            )

            if should_show_popup and self.filtered_items:
                QTimer.singleShot(10, self.show_popup_and_keep_focus)  # 显示下拉列表并保持焦点

        finally:
            self.is_updating = False

            # 如果有待处理的更新，执行它
            if self.update_pending:
                self.update_pending = False
                QTimer.singleShot(50, self.perform_search)  # 减少延迟
    
    def addItem(self, text, userData=None):
        """添加项目 - 优化版本"""
        item_info = {'text': text, 'data': userData}
        self.all_items.append(item_info)

        # 如果当前没有搜索，直接添加到显示和筛选列表
        if not self.current_search:
            self.filtered_items.append(item_info)

            # 标记为程序化改变
            self.is_programmatic_change = True
            super().addItem(text)
            if userData is not None:
                super().setItemData(super().count() - 1, userData, Qt.UserRole)
            self.is_programmatic_change = False
        else:
            # 如果有搜索条件，检查新项目是否匹配
            if self.matches_search(text, self.current_search):
                self.filtered_items.append(item_info)
                # 需要更新显示
                self.update_display()
    
    def clear(self):
        """清空所有项目 - 优化版本"""
        self.all_items.clear()
        self.filtered_items.clear()
        self.current_search = ""
        self.last_search_text = ""
        self.is_updating = False
        self.update_pending = False
        self.should_keep_popup_open = False

        # 标记为程序化改变
        self.is_programmatic_change = True
        super().clear()
        self.is_programmatic_change = False
    
    def mousePressEvent(self, event):
        """鼠标按下事件处理 - 优化版本"""
        # 设置保持下拉列表打开的标志
        self.should_keep_popup_open = True

        # 如果点击下拉按钮，确保显示所有项目
        # 检查点击位置是否在下拉按钮区域
        button_rect = self.rect()
        button_rect.setLeft(button_rect.right() - 20)  # 下拉按钮大约20像素宽

        if button_rect.contains(event.pos()):
            # 点击了下拉按钮
            if self.current_search:
                # 如果有搜索条件，清空搜索并显示所有项目
                self.is_programmatic_change = True
                self.lineEdit().clear()
                self.is_programmatic_change = False

                self.current_search = ""
                self.last_search_text = ""
                self.filtered_items = self.all_items.copy()
                self.update_display()

            # 确保焦点在输入框中
            QTimer.singleShot(10, self.ensure_focus_in_lineedit)

        super().mousePressEvent(event)
    
    def keyPressEvent(self, event):
        """ComboBox级别的键盘事件处理 - 简化版本"""
        # 确保焦点在输入框中，这样键盘输入才有效
        if not self.lineEdit().hasFocus():
            self.lineEdit().setFocus()

        # 对于字符输入，直接传递给输入框处理
        if event.text() and event.text().isprintable():
            # 将字符输入事件传递给输入框
            self.lineEdit().keyPressEvent(event)
            return

        # 其他特殊键由父类处理
        super().keyPressEvent(event)
    
    def count(self):
        """获取项目数量"""
        return len(self.all_items)
    
    def itemText(self, index):
        """获取指定索引的文本"""
        if 0 <= index < len(self.all_items):
            return self.all_items[index]['text']
        return ""
    
    def itemData(self, index, role=Qt.UserRole):
        """获取指定索引的数据"""
        if 0 <= index < len(self.all_items):
            return self.all_items[index]['data']
        return None


class TestAntiFlickerWindow(QMainWindow):
    """测试防闪烁ComboBox的窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_test_data()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("防闪烁ComboBox测试")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("防闪烁ComboBox测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 说明
        info_label = QLabel(
            "🔧 优化的搜索行为:\n"
            "• 智能更新：仅在文本内容实际变化时更新\n"
            "• 持续显示：获得焦点时自动显示并保持下拉列表打开\n"
            "• 实时搜索：输入字母时立即筛选CV项目\n"
            "• 无闪烁体验：优化的更新机制和事件处理\n"
            "• 焦点管理：智能的焦点进出处理"
        )
        info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # 防闪烁ComboBox
        layout.addWidget(QLabel("防闪烁ComboBox:"))
        self.cv_combo = AntiFlickerComboBox()
        layout.addWidget(self.cv_combo)
        
        # 当前选择显示
        self.selection_label = QLabel("当前选择: 无")
        self.selection_label.setStyleSheet("margin: 10px; padding: 5px; background: #e8f4fd; border-radius: 3px;")
        layout.addWidget(self.selection_label)
        
        # 测试按钮
        test_button = QPushButton("重新加载测试数据")
        test_button.clicked.connect(self.load_test_data)
        layout.addWidget(test_button)

        # 焦点测试按钮
        focus_button = QPushButton("测试焦点切换")
        focus_button.clicked.connect(self.test_focus)
        layout.addWidget(focus_button)

        # 状态显示
        self.status_label = QLabel("状态: 就绪")
        self.status_label.setStyleSheet("margin: 5px; padding: 3px; background: #f0f0f0; border-radius: 3px; font-size: 12px;")
        layout.addWidget(self.status_label)

        layout.addStretch()
        
        # 连接信号
        self.cv_combo.currentTextChanged.connect(self.on_selection_changed)
    
    def load_test_data(self):
        """加载测试数据"""
        print("🔄 加载测试数据...")
        
        # 清空现有数据
        self.cv_combo.clear()
        
        # 模拟CV数据
        test_cvs = [
            {"name": "张三", "id": "cv_001"},
            {"name": "李明轩", "id": "cv_002"},
            {"name": "王诗涵", "id": "cv_003"},
            {"name": "陈奕迅", "id": "cv_004"},
            {"name": "小红", "id": "cv_005"},
            {"name": "赵钱孙", "id": "cv_006"},
            {"name": "周杰伦", "id": "cv_007"},
            {"name": "刘德华", "id": "cv_008"},
        ]
        
        # 添加默认选项
        self.cv_combo.addItem("请选择CV...")
        
        # 添加CV数据
        for cv in test_cvs:
            self.cv_combo.addItem(cv["name"], cv["id"])
        
        print(f"✅ 加载完成，总数: {self.cv_combo.count()}")
        self.selection_label.setText("当前选择: 无 (数据已重新加载)")
    
    def test_focus(self):
        """测试焦点切换"""
        if self.cv_combo.lineEdit().hasFocus():
            self.cv_combo.clearFocus()
            self.status_label.setText("状态: 焦点已移除")
        else:
            self.cv_combo.setFocus()
            self.status_label.setText("状态: 焦点已设置到CV下拉框")

    def on_selection_changed(self, text):
        """选择变化处理"""
        if text and text != "请选择CV...":
            cv_id = self.cv_combo.currentData()
            self.selection_label.setText(f"当前选择: {text} (ID: {cv_id})")
            self.status_label.setText(f"状态: 已选择 {text}")
        else:
            self.selection_label.setText("当前选择: 无")
            self.status_label.setText("状态: 无选择")


def main():
    """主函数"""
    print("🎯 防闪烁ComboBox测试")
    print("="*50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestAntiFlickerWindow()
    window.show()
    
    print("✅ 测试窗口已启动")
    print("\n💡 优化功能测试说明:")
    print("  1. 智能更新测试:")
    print("     - 点击输入框获得焦点，下拉列表应自动显示")
    print("     - 输入相同字符不应触发更新")
    print("  2. 持续显示测试:")
    print("     - 获得焦点时下拉列表自动显示")
    print("     - 在输入框有焦点期间，下拉列表保持打开")
    print("  3. 实时搜索测试:")
    print("     - 输入拼音首字母立即筛选 (如 'z', 'zs', 'lm')")
    print("     - 搜索过程中下拉列表不关闭")
    print("  4. 用户体验测试:")
    print("     - 点击下拉按钮无闪烁")
    print("     - 按Escape键清空搜索但保持下拉列表")
    print("     - 使用'测试焦点切换'按钮验证焦点行为")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
