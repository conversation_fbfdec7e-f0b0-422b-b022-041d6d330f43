#!/usr/bin/env python3
"""测试CV下拉框修复

此脚本测试修复后的CV下拉框功能。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import Qt
from presentation.gui.searchable_combobox import SearchableComboBox


class CVComboBoxTestWindow(QMainWindow):
    """CV下拉框测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.test_cv_data()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("CV下拉框修复测试")
        self.setGeometry(100, 100, 500, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("CV下拉框修复测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 测试说明
        info_label = QLabel(
            "🔧 修复内容:\n"
            "• 修复了addItem时数据传递的问题\n"
            "• 改进了update_cv_combo的实现\n"
            "• 增加了详细的调试日志\n"
            "• 验证SearchableComboBox的数据存储"
        )
        info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # SearchableComboBox测试
        layout.addWidget(QLabel("修复后的SearchableComboBox:"))
        self.cv_combo = SearchableComboBox()
        layout.addWidget(self.cv_combo)
        
        # 当前选择显示
        self.selection_label = QLabel("当前选择: 无")
        self.selection_label.setStyleSheet("margin: 10px; padding: 5px; background: #e8f4fd; border-radius: 3px;")
        layout.addWidget(self.selection_label)
        
        # 测试按钮
        test_button = QPushButton("重新加载测试数据")
        test_button.clicked.connect(self.test_cv_data)
        layout.addWidget(test_button)
        
        # 获取选择按钮
        get_button = QPushButton("获取当前选择")
        get_button.clicked.connect(self.get_current_selection)
        layout.addWidget(get_button)
        
        layout.addStretch()
        
        # 连接信号
        self.cv_combo.currentTextChanged.connect(self.on_selection_changed)
    
    def test_cv_data(self):
        """测试CV数据加载"""
        print("\n" + "="*50)
        print("🧪 测试CV数据加载")
        print("="*50)
        
        # 模拟主窗口的update_cv_combo逻辑
        test_cvs = [
            {"id": "cv_001", "name": "张三"},
            {"id": "cv_002", "name": "李明轩"},
            {"cvId": "cv_003", "cvName": "王诗涵"},
            {"cvId": "cv_004", "cvName": "陈奕迅"},
            {"id": "cv_005", "name": "小红"},
            {"cvId": "cv_006", "cvName": "赵钱孙"},
        ]
        
        print(f"🔄 开始更新CV下拉框，CV数量: {len(test_cvs)}")
        
        # 清空并重新加载
        self.cv_combo.clear()
        self.cv_combo.addItem("请选择CV...")
        
        for i, cv in enumerate(test_cvs):
            # 兼容不同的API数据结构
            cv_name = (cv.get('name') or cv.get('cvName') or
                      cv.get('id') or str(cv.get('cvId')) or '未知CV')
            cv_id = cv.get('id') or str(cv.get('cvId')) or None
            
            print(f"   添加CV {i+1}: '{cv_name}' (ID: {cv_id})")
            
            # 直接在addItem时传递数据
            self.cv_combo.addItem(cv_name, cv_id)
        
        self.cv_combo.setEnabled(True)
        print(f"📋 CV列表已更新，共 {len(test_cvs)} 个CV")
        
        # 验证添加的项目
        print(f"🔍 验证: ComboBox项目数量 = {self.cv_combo.count()}")
        for i in range(self.cv_combo.count()):
            text = self.cv_combo.itemText(i)
            data = self.cv_combo.itemData(i)
            print(f"   项目 {i}: '{text}' (数据: {data})")
        
        # 更新状态
        self.selection_label.setText("当前选择: 无 (数据已重新加载)")
    
    def on_selection_changed(self, text):
        """选择变化处理"""
        if text and text != "请选择CV...":
            cv_id = self.cv_combo.currentData()
            self.selection_label.setText(f"当前选择: {text} (ID: {cv_id})")
            print(f"📝 选择变化: '{text}' (ID: {cv_id})")
        else:
            self.selection_label.setText("当前选择: 无")
    
    def get_current_selection(self):
        """获取当前选择"""
        current_text = self.cv_combo.currentText()
        current_data = self.cv_combo.currentData()
        current_index = self.cv_combo.currentIndex()
        
        print(f"\n📊 当前选择信息:")
        print(f"   文本: '{current_text}'")
        print(f"   数据: {current_data}")
        print(f"   索引: {current_index}")
        
        # 显示内部状态
        print(f"\n🔍 内部状态:")
        print(f"   原始项目数量: {len(self.cv_combo.original_items)}")
        print(f"   原始数据数量: {len(self.cv_combo.original_data)}")
        print(f"   模型行数: {self.cv_combo.model.rowCount()}")
        
        from PyQt5.QtWidgets import QMessageBox
        info = f"当前选择:\n\n文本: {current_text}\n数据: {current_data}\n索引: {current_index}"
        QMessageBox.information(self, "当前选择", info)


def test_searchable_combobox_basic():
    """测试SearchableComboBox基本功能"""
    print("🔍 测试SearchableComboBox基本功能")
    
    try:
        # 创建测试实例
        combo = SearchableComboBox()
        print("✅ SearchableComboBox创建成功")
        
        # 测试addItem方法（新的方式）
        combo.addItem("测试项目1", "data1")
        combo.addItem("测试项目2", "data2")
        combo.addItem("张三", "cv_001")
        combo.addItem("李明轩", "cv_002")
        
        print(f"✅ addItem方法正常，项目数量: {combo.count()}")
        
        # 测试数据获取
        for i in range(combo.count()):
            text = combo.itemText(i)
            data = combo.itemData(i)
            print(f"   项目 {i}: '{text}' (数据: {data})")
        
        # 测试搜索功能
        print("\n🔍 测试搜索功能:")
        combo.lineEdit().setText("z")
        print(f"   输入 'z' 后的筛选状态")
        
        return True
        
    except Exception as e:
        print(f"❌ SearchableComboBox测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🎯 CV下拉框修复测试")
    print("="*50)
    
    # 先测试基本功能
    test_searchable_combobox_basic()
    
    print("\n" + "="*50)
    print("🖥️ 启动GUI测试窗口...")
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = CVComboBoxTestWindow()
    window.show()
    
    print("✅ GUI测试窗口已启动")
    print("\n💡 测试说明:")
    print("  1. 窗口显示了修复后的SearchableComboBox")
    print("  2. 点击'重新加载测试数据'测试数据加载")
    print("  3. 尝试在下拉框中选择不同的CV")
    print("  4. 测试拼音搜索功能 (输入 'z', 'zs', 'lm' 等)")
    print("  5. 点击'获取当前选择'查看选择状态")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
