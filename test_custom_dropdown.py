#!/usr/bin/env python3
"""测试自定义下拉框控件

测试QLineEdit + QListWidget组合实现的下拉框功能。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from custom_dropdown_widget import CustomDropdownWidget


class CustomDropdownTestWindow(QMainWindow):
    """自定义下拉框测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_test_data()
        self.setup_monitoring()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("自定义下拉框控件测试")
        self.setGeometry(100, 100, 700, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("自定义下拉框控件测试 (QLineEdit + QListWidget)")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 测试说明
        info_label = QLabel(
            "🔧 自定义下拉框特性:\n"
            "• QLineEdit + QListWidget组合设计\n"
            "• 完全控制的交互行为和键盘输入\n"
            "• 智能的焦点管理和列表显示\n"
            "• 保留所有拼音搜索功能\n"
            "• ComboBox兼容接口，可无缝替换"
        )
        info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # 自定义下拉框
        layout.addWidget(QLabel("自定义CV下拉框:"))
        self.cv_dropdown = CustomDropdownWidget()
        self.cv_dropdown.setFixedHeight(30)
        layout.addWidget(self.cv_dropdown)
        
        # 状态显示区域
        status_layout = QHBoxLayout()
        
        # 焦点状态
        self.focus_status = QLabel("焦点: 未知")
        self.focus_status.setStyleSheet("margin: 5px; padding: 5px; background: #e8f4fd; border-radius: 3px; font-weight: bold;")
        status_layout.addWidget(self.focus_status)
        
        # 列表状态
        self.list_status = QLabel("列表: 未知")
        self.list_status.setStyleSheet("margin: 5px; padding: 5px; background: #f0f8ff; border-radius: 3px; font-weight: bold;")
        status_layout.addWidget(self.list_status)
        
        layout.addLayout(status_layout)
        
        # 当前输入显示
        self.input_display = QLabel("当前输入: (空)")
        self.input_display.setStyleSheet("margin: 10px; padding: 5px; background: #fff3cd; border-radius: 3px; font-family: monospace;")
        layout.addWidget(self.input_display)
        
        # 当前选择显示
        self.selection_label = QLabel("当前选择: 无")
        self.selection_label.setStyleSheet("margin: 10px; padding: 5px; background: #d4edda; border-radius: 3px;")
        layout.addWidget(self.selection_label)
        
        # 测试按钮
        button_layout = QHBoxLayout()
        
        focus_button = QPushButton("设置焦点")
        focus_button.clicked.connect(self.set_focus)
        button_layout.addWidget(focus_button)
        
        clear_button = QPushButton("清空输入")
        clear_button.clicked.connect(self.clear_input)
        button_layout.addWidget(clear_button)
        
        reload_button = QPushButton("重新加载数据")
        reload_button.clicked.connect(self.load_test_data)
        button_layout.addWidget(reload_button)
        
        test_api_button = QPushButton("测试API兼容性")
        test_api_button.clicked.connect(self.test_api_compatibility)
        button_layout.addWidget(test_api_button)
        
        layout.addLayout(button_layout)
        
        # 事件日志
        layout.addWidget(QLabel("交互事件日志:"))
        self.event_log = QTextEdit()
        self.event_log.setMaximumHeight(200)
        self.event_log.setStyleSheet("font-family: monospace; font-size: 12px;")
        layout.addWidget(self.event_log)
        
        layout.addStretch()
        
        # 连接信号
        self.cv_dropdown.currentTextChanged.connect(self.on_text_changed)
        self.cv_dropdown.itemSelected.connect(self.on_item_selected)
    
    def load_test_data(self):
        """加载测试数据"""
        # 清空现有数据
        self.cv_dropdown.clear()
        
        # 模拟CV数据
        test_cvs = [
            {"name": "张三", "id": "cv_001"},
            {"name": "张学友", "id": "cv_002"},
            {"name": "张国荣", "id": "cv_003"},
            {"name": "李明轩", "id": "cv_004"},
            {"name": "李小龙", "id": "cv_005"},
            {"name": "李连杰", "id": "cv_006"},
            {"name": "王诗涵", "id": "cv_007"},
            {"name": "王菲", "id": "cv_008"},
            {"name": "王力宏", "id": "cv_009"},
            {"name": "陈奕迅", "id": "cv_010"},
            {"name": "陈慧琳", "id": "cv_011"},
            {"name": "周杰伦", "id": "cv_012"},
            {"name": "周星驰", "id": "cv_013"},
            {"name": "刘德华", "id": "cv_014"},
            {"name": "刘嘉玲", "id": "cv_015"},
        ]
        
        # 添加默认选项
        self.cv_dropdown.addItem("请选择CV...")
        
        # 添加CV数据
        for cv in test_cvs:
            self.cv_dropdown.addItem(cv["name"], cv["id"])
        
        self.log_event("数据加载完成", f"共{len(test_cvs)}个CV")
    
    def setup_monitoring(self):
        """设置状态监控"""
        # 创建定时器监控状态
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_status)
        self.monitor_timer.start(100)  # 每100ms检查一次状态
    
    def update_status(self):
        """更新状态显示"""
        # 更新焦点状态
        if self.cv_dropdown.hasFocus():
            self.focus_status.setText("焦点: ✅ 在输入框")
            self.focus_status.setStyleSheet("margin: 5px; padding: 5px; background: #d4edda; border-radius: 3px; font-weight: bold; color: #155724;")
        else:
            self.focus_status.setText("焦点: ❌ 不在输入框")
            self.focus_status.setStyleSheet("margin: 5px; padding: 5px; background: #f8d7da; border-radius: 3px; font-weight: bold; color: #721c24;")
        
        # 更新列表状态
        if self.cv_dropdown.list_widget.isVisible():
            self.list_status.setText("列表: ✅ 显示中")
            self.list_status.setStyleSheet("margin: 5px; padding: 5px; background: #d4edda; border-radius: 3px; font-weight: bold; color: #155724;")
        else:
            self.list_status.setText("列表: ❌ 隐藏")
            self.list_status.setStyleSheet("margin: 5px; padding: 5px; background: #f8d7da; border-radius: 3px; font-weight: bold; color: #721c24;")
    
    def log_event(self, event_type, details=""):
        """记录事件"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        focus_info = "✅输入框" if self.cv_dropdown.hasFocus() else "❌无焦点"
        list_info = "✅显示" if self.cv_dropdown.list_widget.isVisible() else "❌隐藏"
        
        log_entry = f"[{timestamp}] {event_type}"
        if details:
            log_entry += f" - {details}"
        log_entry += f" (焦点:{focus_info}, 列表:{list_info})"
        
        self.event_log.append(log_entry)
        
        # 自动滚动到底部
        cursor = self.event_log.textCursor()
        cursor.movePosition(cursor.End)
        self.event_log.setTextCursor(cursor)
    
    def on_text_changed(self, text):
        """文本变化处理"""
        self.input_display.setText(f"当前输入: '{text}'" if text else "当前输入: (空)")
        self.log_event("文本变化", f"'{text}'")
    
    def on_item_selected(self, text, data):
        """项目选择处理"""
        self.selection_label.setText(f"当前选择: {text} (ID: {data})")
        self.log_event("项目选择", f"'{text}' (ID: {data})")
    
    def set_focus(self):
        """设置焦点到输入框"""
        self.cv_dropdown.setFocus()
        self.log_event("手动设置焦点")
    
    def clear_input(self):
        """清空输入框"""
        self.cv_dropdown.setCurrentText("")
        self.log_event("手动清空输入")
    
    def test_api_compatibility(self):
        """测试API兼容性"""
        self.log_event("开始API兼容性测试")
        
        # 测试ComboBox兼容接口
        try:
            # 测试基本方法
            count = self.cv_dropdown.count()
            current_text = self.cv_dropdown.currentText()
            current_data = self.cv_dropdown.currentData()
            
            # 测试索引访问
            if count > 1:
                item_text = self.cv_dropdown.itemText(1)
                item_data = self.cv_dropdown.itemData(1)
                
                self.log_event("API测试成功", f"count={count}, text='{current_text}', item1='{item_text}'")
            else:
                self.log_event("API测试成功", f"count={count}, text='{current_text}'")
                
        except Exception as e:
            self.log_event("API测试失败", str(e))


def main():
    """主函数"""
    print("🔧 自定义下拉框控件测试")
    print("="*50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = CustomDropdownTestWindow()
    window.show()
    
    print("✅ 自定义下拉框测试窗口已启动")
    print("\n💡 测试功能:")
    print("  1. 组件组合设计:")
    print("     - QLineEdit作为输入框")
    print("     - QListWidget作为下拉列表")
    print("     - 视觉上统一的下拉框效果")
    print("  2. 交互行为:")
    print("     - 点击输入框获得焦点，列表自动显示")
    print("     - 输入字母实时筛选CV项目")
    print("     - 键盘导航：上下箭头、回车、Escape")
    print("     - 点击列表项目选择CV")
    print("     - 失去焦点时隐藏列表")
    print("  3. 功能保持:")
    print("     - 拼音搜索功能完整")
    print("     - 智能更新机制")
    print("     - 防闪烁特性")
    print("     - 键盘输入始终有效")
    print("  4. ComboBox兼容:")
    print("     - 提供完整的ComboBox API")
    print("     - 可无缝替换现有ComboBox")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
