#!/usr/bin/env python3
"""测试CV下拉框键盘输入有效性

专门测试当下拉列表显示时，键盘输入是否仍然有效。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer
from anti_flicker_combobox import AntiFlickerComboBox


class KeyboardInputTestWindow(QMainWindow):
    """键盘输入测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.load_test_data()
        self.setup_monitoring()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("CV下拉框键盘输入测试")
        self.setGeometry(100, 100, 700, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title_label = QLabel("CV下拉框键盘输入有效性测试")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin: 10px;")
        layout.addWidget(title_label)
        
        # 测试说明
        info_label = QLabel(
            "⌨️ 键盘输入测试:\n"
            "• 点击输入框获得焦点，下拉列表应自动显示\n"
            "• 输入字母时，下拉列表保持显示，字母应出现在输入框中\n"
            "• 继续输入更多字母，应该能连续筛选\n"
            "• 使用上下箭头键导航下拉列表\n"
            "• 按回车键选择项目\n"
            "• 按Escape键清空搜索\n"
            "• 观察下方的实时状态和输入日志"
        )
        info_label.setStyleSheet("color: #666; margin: 10px; padding: 10px; background: #f5f5f5; border-radius: 5px;")
        layout.addWidget(info_label)
        
        # CV下拉框
        layout.addWidget(QLabel("测试CV下拉框:"))
        self.cv_combo = AntiFlickerComboBox()
        layout.addWidget(self.cv_combo)
        
        # 状态显示区域
        status_layout = QHBoxLayout()
        
        # 焦点状态
        self.focus_status = QLabel("焦点: 未知")
        self.focus_status.setStyleSheet("margin: 5px; padding: 5px; background: #e8f4fd; border-radius: 3px; font-weight: bold;")
        status_layout.addWidget(self.focus_status)
        
        # 下拉列表状态
        self.popup_status = QLabel("下拉列表: 未知")
        self.popup_status.setStyleSheet("margin: 5px; padding: 5px; background: #f0f8ff; border-radius: 3px; font-weight: bold;")
        status_layout.addWidget(self.popup_status)
        
        layout.addLayout(status_layout)
        
        # 当前输入显示
        self.input_display = QLabel("当前输入: (空)")
        self.input_display.setStyleSheet("margin: 10px; padding: 5px; background: #fff3cd; border-radius: 3px; font-family: monospace;")
        layout.addWidget(self.input_display)
        
        # 当前选择显示
        self.selection_label = QLabel("当前选择: 无")
        self.selection_label.setStyleSheet("margin: 10px; padding: 5px; background: #d4edda; border-radius: 3px;")
        layout.addWidget(self.selection_label)
        
        # 测试按钮
        button_layout = QHBoxLayout()
        
        focus_button = QPushButton("设置焦点")
        focus_button.clicked.connect(self.set_focus)
        button_layout.addWidget(focus_button)
        
        clear_button = QPushButton("清空输入")
        clear_button.clicked.connect(self.clear_input)
        button_layout.addWidget(clear_button)
        
        show_popup_button = QPushButton("显示下拉列表")
        show_popup_button.clicked.connect(self.show_popup)
        button_layout.addWidget(show_popup_button)
        
        simulate_input_button = QPushButton("模拟输入'张'")
        simulate_input_button.clicked.connect(self.simulate_input)
        button_layout.addWidget(simulate_input_button)
        
        layout.addLayout(button_layout)
        
        # 输入事件日志
        layout.addWidget(QLabel("键盘输入事件日志:"))
        self.input_log = QTextEdit()
        self.input_log.setMaximumHeight(200)
        self.input_log.setStyleSheet("font-family: monospace; font-size: 12px;")
        layout.addWidget(self.input_log)
        
        layout.addStretch()
        
        # 连接信号
        self.cv_combo.currentTextChanged.connect(self.on_text_changed)
        self.cv_combo.lineEdit().textChanged.connect(self.on_input_changed)
    
    def load_test_data(self):
        """加载测试数据"""
        # 清空现有数据
        self.cv_combo.clear()
        
        # 模拟CV数据
        test_cvs = [
            {"name": "张三", "id": "cv_001"},
            {"name": "张学友", "id": "cv_002"},
            {"name": "张国荣", "id": "cv_003"},
            {"name": "李明轩", "id": "cv_004"},
            {"name": "李小龙", "id": "cv_005"},
            {"name": "王诗涵", "id": "cv_006"},
            {"name": "王菲", "id": "cv_007"},
            {"name": "陈奕迅", "id": "cv_008"},
            {"name": "陈慧琳", "id": "cv_009"},
            {"name": "周杰伦", "id": "cv_010"},
            {"name": "周星驰", "id": "cv_011"},
            {"name": "刘德华", "id": "cv_012"},
            {"name": "刘嘉玲", "id": "cv_013"},
        ]
        
        # 添加默认选项
        self.cv_combo.addItem("请选择CV...")
        
        # 添加CV数据
        for cv in test_cvs:
            self.cv_combo.addItem(cv["name"], cv["id"])
        
        self.log_event("数据加载完成", f"共{len(test_cvs)}个CV")
    
    def setup_monitoring(self):
        """设置状态监控"""
        # 创建定时器监控状态
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.update_status)
        self.monitor_timer.start(100)  # 每100ms检查一次状态
    
    def update_status(self):
        """更新状态显示"""
        # 更新焦点状态
        if self.cv_combo.lineEdit().hasFocus():
            self.focus_status.setText("焦点: ✅ 在输入框")
            self.focus_status.setStyleSheet("margin: 5px; padding: 5px; background: #d4edda; border-radius: 3px; font-weight: bold; color: #155724;")
        elif self.cv_combo.hasFocus():
            self.focus_status.setText("焦点: ⚠️ 在ComboBox")
            self.focus_status.setStyleSheet("margin: 5px; padding: 5px; background: #fff3cd; border-radius: 3px; font-weight: bold; color: #856404;")
        else:
            self.focus_status.setText("焦点: ❌ 不在CV框")
            self.focus_status.setStyleSheet("margin: 5px; padding: 5px; background: #f8d7da; border-radius: 3px; font-weight: bold; color: #721c24;")
        
        # 更新下拉列表状态
        if self.cv_combo.view().isVisible():
            self.popup_status.setText("下拉列表: ✅ 显示中")
            self.popup_status.setStyleSheet("margin: 5px; padding: 5px; background: #d4edda; border-radius: 3px; font-weight: bold; color: #155724;")
        else:
            self.popup_status.setText("下拉列表: ❌ 隐藏")
            self.popup_status.setStyleSheet("margin: 5px; padding: 5px; background: #f8d7da; border-radius: 3px; font-weight: bold; color: #721c24;")
    
    def log_event(self, event_type, details=""):
        """记录事件"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        
        focus_info = "✅输入框" if self.cv_combo.lineEdit().hasFocus() else "❌无焦点"
        popup_info = "✅显示" if self.cv_combo.view().isVisible() else "❌隐藏"
        
        log_entry = f"[{timestamp}] {event_type}"
        if details:
            log_entry += f" - {details}"
        log_entry += f" (焦点:{focus_info}, 下拉:{popup_info})"
        
        self.input_log.append(log_entry)
        
        # 自动滚动到底部
        cursor = self.input_log.textCursor()
        cursor.movePosition(cursor.End)
        self.input_log.setTextCursor(cursor)
    
    def on_input_changed(self, text):
        """输入框文本变化"""
        self.input_display.setText(f"当前输入: '{text}'" if text else "当前输入: (空)")
        self.log_event("输入变化", f"'{text}'")
    
    def on_text_changed(self, text):
        """选择变化处理"""
        if text and text != "请选择CV...":
            cv_id = self.cv_combo.currentData()
            self.selection_label.setText(f"当前选择: {text} (ID: {cv_id})")
            self.log_event("选择变化", f"'{text}'")
        else:
            self.selection_label.setText("当前选择: 无")
            self.log_event("选择清空")
    
    def set_focus(self):
        """设置焦点到输入框"""
        self.cv_combo.lineEdit().setFocus()
        self.log_event("手动设置焦点")
    
    def clear_input(self):
        """清空输入框"""
        self.cv_combo.lineEdit().clear()
        self.log_event("手动清空输入")
    
    def show_popup(self):
        """显示下拉列表"""
        self.cv_combo.showPopup()
        self.log_event("手动显示下拉列表")
    
    def simulate_input(self):
        """模拟输入字符"""
        from PyQt5.QtGui import QKeyEvent
        from PyQt5.QtCore import QEvent
        
        # 确保焦点在输入框
        self.cv_combo.lineEdit().setFocus()
        
        # 模拟按下'张'字符
        key_event = QKeyEvent(QEvent.KeyPress, ord('张'), Qt.NoModifier, '张')
        self.cv_combo.lineEdit().keyPressEvent(key_event)
        
        self.log_event("模拟输入", "'张'字符")


def main():
    """主函数"""
    print("⌨️ CV下拉框键盘输入测试")
    print("="*50)
    
    # 创建应用程序
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = KeyboardInputTestWindow()
    window.show()
    
    print("✅ 键盘输入测试窗口已启动")
    print("\n💡 测试步骤:")
    print("  1. 点击CV下拉框的输入区域获得焦点")
    print("  2. 观察下拉列表是否自动显示")
    print("  3. 直接在键盘上输入字母 'z' 或 '张'")
    print("  4. 观察字母是否出现在输入框中")
    print("  5. 继续输入更多字母，观察筛选效果")
    print("  6. 使用上下箭头键导航下拉列表")
    print("  7. 按回车键选择项目")
    print("  8. 按Escape键清空搜索")
    print("  9. 查看下方的事件日志了解详细情况")
    print("\n🔍 关键测试点:")
    print("  • 下拉列表显示时，键盘输入应该仍然有效")
    print("  • 焦点应该始终保持在输入框中")
    print("  • 输入的字符应该立即出现在输入框中")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
