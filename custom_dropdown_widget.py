#!/usr/bin/env python3
"""自定义下拉框控件

使用QLineEdit + QListWidget组合实现的下拉框，解决ComboBox的技术难题。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import (QWidget, QLineEdit, QListWidget, QVBoxLayout,
                             QListWidgetItem, QApplication, QFrame)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QEvent
from PyQt5.QtGui import QFont
from utils.pinyin_helper import PinyinHelper


class CustomDropdownWidget(QWidget):
    """自定义下拉框控件 - QLineEdit + QListWidget组合"""
    
    # 信号定义
    currentTextChanged = pyqtSignal(str)  # 当前文本变化信号
    itemSelected = pyqtSignal(str, object)  # 项目选择信号 (text, data)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 数据存储
        self.all_items = []  # [{'text': str, 'data': any}, ...]
        self.filtered_items = []  # 当前筛选后的项目
        self.current_search = ""
        self.last_search_text = ""
        
        # 状态控制
        self.is_updating = False
        self.is_programmatic_change = False
        self.should_show_list = False
        
        self.init_ui()
        self.setup_functionality()
    
    def init_ui(self):
        """初始化UI组件"""
        # 设置主布局
        self.setFixedHeight(25)  # 设置控件高度
        
        # 创建输入框
        self.line_edit = QLineEdit(self)
        self.line_edit.setPlaceholderText("请选择CV...")
        self.line_edit.setStyleSheet("""
            QLineEdit {
                border: 1px solid #ccc;
                border-radius: 3px;
                padding: 2px 5px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border: 2px solid #0078d4;
            }
        """)
        
        # 创建列表框
        self.list_widget = QListWidget(self)
        self.list_widget.setWindowFlags(Qt.Popup | Qt.FramelessWindowHint)
        self.list_widget.setFocusPolicy(Qt.NoFocus)  # 防止列表获得焦点
        self.list_widget.setStyleSheet("""
            QListWidget {
                border: 1px solid #ccc;
                border-top: none;
                background-color: white;
                selection-background-color: #0078d4;
                selection-color: white;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 4px 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QListWidget::item:hover {
                background-color: #f0f8ff;
            }
            QListWidget::item:selected {
                background-color: #0078d4;
                color: white;
            }
        """)
        self.list_widget.hide()
        
        # 设置输入框位置和大小
        self.line_edit.setGeometry(0, 0, self.width(), self.height())

    def eventFilter(self, obj, event):
        """事件过滤器 - 安全处理输入框事件"""
        if obj == self.line_edit:
            if event.type() == QEvent.FocusIn:
                return self.handle_focus_in(event)
            elif event.type() == QEvent.FocusOut:
                return self.handle_focus_out(event)
            elif event.type() == QEvent.KeyPress:
                return self.handle_key_press(event)

        return super().eventFilter(obj, event)
    
    def setup_functionality(self):
        """设置功能连接"""
        # 连接输入框信号
        self.line_edit.textChanged.connect(self.on_text_changed)

        # 安装事件过滤器而不是直接重写方法，避免死锁
        self.line_edit.installEventFilter(self)

        # 连接列表框信号
        self.list_widget.itemClicked.connect(self.on_item_clicked)
        self.list_widget.itemActivated.connect(self.on_item_activated)

        # 设置搜索定时器
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.perform_search)

        # 设置隐藏定时器
        self.hide_timer = QTimer()
        self.hide_timer.setSingleShot(True)
        self.hide_timer.timeout.connect(self.hide_list)
    
    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        # 调整输入框大小
        self.line_edit.setGeometry(0, 0, self.width(), self.height())
        # 如果列表可见，调整列表位置
        if self.list_widget.isVisible():
            self.position_list()
    
    def handle_focus_in(self, event):
        """处理输入框获得焦点事件"""
        self.should_show_list = True

        # 如果有数据，显示列表
        if self.all_items:
            if not self.current_search:
                self.filtered_items = self.all_items.copy()
                self.update_list()
            # 延迟显示列表，避免事件冲突
            QTimer.singleShot(10, self.show_list)

        return False  # 继续传递事件

    def handle_focus_out(self, event):
        """处理输入框失去焦点事件"""
        self.should_show_list = False

        # 延迟隐藏列表，给点击列表项留时间
        self.hide_timer.start(150)

        return False  # 继续传递事件

    def handle_key_press(self, event):
        """处理键盘事件"""
        # Escape键：清空搜索并隐藏列表
        if event.key() == Qt.Key_Escape:
            self.is_programmatic_change = True
            self.line_edit.clear()
            self.is_programmatic_change = False

            self.current_search = ""
            self.last_search_text = ""
            self.filtered_items = self.all_items.copy()
            self.update_list()
            self.hide_list()
            return True  # 阻止事件继续传递

        # 上下箭头键：导航列表
        elif event.key() == Qt.Key_Down:
            if self.list_widget.isVisible():
                current_row = self.list_widget.currentRow()
                if current_row < self.list_widget.count() - 1:
                    self.list_widget.setCurrentRow(current_row + 1)
                else:
                    self.list_widget.setCurrentRow(0)
                return True  # 阻止事件继续传递
            else:
                # 如果列表未显示，显示列表
                self.show_list()
                return True

        elif event.key() == Qt.Key_Up:
            if self.list_widget.isVisible():
                current_row = self.list_widget.currentRow()
                if current_row > 0:
                    self.list_widget.setCurrentRow(current_row - 1)
                else:
                    self.list_widget.setCurrentRow(self.list_widget.count() - 1)
                return True  # 阻止事件继续传递

        # 回车键：选择当前项目
        elif event.key() in (Qt.Key_Return, Qt.Key_Enter):
            if self.list_widget.isVisible():
                current_item = self.list_widget.currentItem()
                if current_item:
                    self.select_item(current_item)
                return True  # 阻止事件继续传递

        # 其他键：让输入框正常处理
        return False
    
    def on_text_changed(self, text):
        """文本变化处理"""
        if self.is_programmatic_change:
            return
        
        new_search_text = text.strip()
        
        # 智能更新：仅在文本实际变化时更新
        if new_search_text == self.last_search_text:
            return
        
        self.current_search = new_search_text
        self.last_search_text = new_search_text
        
        # 发送文本变化信号
        self.currentTextChanged.emit(text)
        
        # 如果正在更新，跳过
        if self.is_updating:
            return
        
        # 立即搜索
        self.perform_search()
    
    def perform_search(self):
        """执行搜索"""
        if self.is_updating:
            return
        
        search_text = self.current_search
        
        if not search_text:
            # 搜索为空，显示所有项目
            self.filtered_items = self.all_items.copy()
        else:
            # 筛选匹配的项目
            self.filtered_items = []
            for item_info in self.all_items:
                text = item_info['text']
                
                # 跳过默认提示项
                if text in ["请选择CV...", "请先选择书籍...", "CV加载失败", "暂无可用CV"]:
                    self.filtered_items.append(item_info)
                    continue
                
                # 使用拼音助手进行匹配
                if self.matches_search(text, search_text):
                    self.filtered_items.append(item_info)
        
        # 更新列表显示
        self.update_list()
        
        # 如果应该显示列表且有结果，显示列表
        if self.should_show_list and self.filtered_items:
            self.show_list()
    
    def matches_search(self, text, search_text):
        """检查文本是否匹配搜索"""
        if not search_text:
            return True
        
        # 使用拼音助手进行匹配
        item_pinyin = PinyinHelper.get_text_pinyin_letters(text)
        search_upper = search_text.upper()
        
        # 支持多种匹配方式
        return (search_upper in item_pinyin or                    # 拼音首字母匹配
                search_upper in text.upper() or                   # 直接文本匹配
                item_pinyin.startswith(search_upper))              # 拼音开头匹配
    
    def update_list(self):
        """更新列表内容"""
        if self.is_updating:
            return

        self.is_updating = True

        try:
            # 暂时断开信号连接，避免递归
            self.list_widget.itemClicked.disconnect()
            self.list_widget.itemActivated.disconnect()

            # 清空列表
            self.list_widget.clear()

            # 添加筛选后的项目
            for item_info in self.filtered_items:
                item = QListWidgetItem(item_info['text'])
                item.setData(Qt.UserRole, item_info['data'])
                self.list_widget.addItem(item)

            # 调整列表高度
            self.adjust_list_height()

            # 重新连接信号
            self.list_widget.itemClicked.connect(self.on_item_clicked)
            self.list_widget.itemActivated.connect(self.on_item_activated)

        except Exception as e:
            print(f"更新列表时出错: {e}")
        finally:
            self.is_updating = False
    
    def adjust_list_height(self):
        """调整列表高度"""
        if self.list_widget.count() == 0:
            return
        
        # 计算合适的高度
        item_height = 25  # 每个项目的高度
        max_visible_items = 8  # 最多显示8个项目
        visible_items = min(self.list_widget.count(), max_visible_items)
        
        list_height = visible_items * item_height + 4  # 加上边框
        self.list_widget.setFixedHeight(list_height)
    
    def position_list(self):
        """定位列表位置"""
        # 获取输入框在全局坐标系中的位置
        global_pos = self.line_edit.mapToGlobal(self.line_edit.rect().bottomLeft())
        
        # 设置列表位置和宽度
        self.list_widget.move(global_pos)
        self.list_widget.setFixedWidth(self.line_edit.width())
    
    def show_list(self):
        """显示列表"""
        if self.list_widget.count() > 0:
            self.position_list()
            self.list_widget.show()
            self.list_widget.raise_()
            
            # 选择第一个项目
            if self.list_widget.count() > 0:
                self.list_widget.setCurrentRow(0)
    
    def hide_list(self):
        """隐藏列表"""
        self.list_widget.hide()
    
    def on_item_clicked(self, item):
        """列表项目点击"""
        self.select_item(item)
    
    def on_item_activated(self, item):
        """列表项目激活"""
        self.select_item(item)
    
    def select_item(self, item):
        """选择项目"""
        if not item:
            return
        
        text = item.text()
        data = item.data(Qt.UserRole)
        
        # 设置输入框文本
        self.is_programmatic_change = True
        self.line_edit.setText(text)
        self.is_programmatic_change = False
        
        # 更新搜索状态
        self.current_search = text
        self.last_search_text = text
        
        # 隐藏列表
        self.hide_list()
        
        # 发送选择信号
        self.itemSelected.emit(text, data)
        self.currentTextChanged.emit(text)
        
        # 确保焦点回到输入框
        self.line_edit.setFocus()
    
    # ComboBox兼容接口
    def addItem(self, text, userData=None):
        """添加项目 - ComboBox兼容接口"""
        item_info = {'text': text, 'data': userData}
        self.all_items.append(item_info)
        
        # 如果当前没有搜索，添加到筛选列表
        if not self.current_search:
            self.filtered_items.append(item_info)
            if not self.is_updating:
                self.update_list()
    
    def clear(self):
        """清空所有项目 - ComboBox兼容接口"""
        self.all_items.clear()
        self.filtered_items.clear()
        self.current_search = ""
        self.last_search_text = ""
        self.is_updating = False
        
        self.list_widget.clear()
        self.is_programmatic_change = True
        self.line_edit.clear()
        self.is_programmatic_change = False
        
        self.hide_list()
    
    def currentText(self):
        """获取当前文本 - ComboBox兼容接口"""
        return self.line_edit.text()
    
    def currentData(self, role=Qt.UserRole):
        """获取当前数据 - ComboBox兼容接口"""
        current_text = self.currentText()
        
        # 在所有项目中查找匹配的数据
        for item_info in self.all_items:
            if item_info['text'] == current_text:
                return item_info['data']
        
        return None
    
    def setCurrentText(self, text):
        """设置当前文本 - ComboBox兼容接口"""
        self.is_programmatic_change = True
        self.line_edit.setText(text)
        self.is_programmatic_change = False
        
        self.current_search = text
        self.last_search_text = text
    
    def count(self):
        """获取项目数量 - ComboBox兼容接口"""
        return len(self.all_items)
    
    def itemText(self, index):
        """获取指定索引的文本 - ComboBox兼容接口"""
        if 0 <= index < len(self.all_items):
            return self.all_items[index]['text']
        return ""
    
    def itemData(self, index, role=Qt.UserRole):
        """获取指定索引的数据 - ComboBox兼容接口"""
        if 0 <= index < len(self.all_items):
            return self.all_items[index]['data']
        return None
    
    def setEnabled(self, enabled):
        """设置启用状态 - ComboBox兼容接口"""
        super().setEnabled(enabled)
        self.line_edit.setEnabled(enabled)
        if not enabled:
            self.hide_list()
    
    def setFocus(self):
        """设置焦点 - ComboBox兼容接口"""
        self.line_edit.setFocus()
    
    def hasFocus(self):
        """检查是否有焦点 - ComboBox兼容接口"""
        return self.line_edit.hasFocus()
    
    def lineEdit(self):
        """获取输入框 - ComboBox兼容接口"""
        return self.line_edit
