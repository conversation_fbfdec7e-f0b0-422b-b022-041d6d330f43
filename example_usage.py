#!/usr/bin/env python3
"""拼音筛选功能使用示例

展示如何在代码中使用新的拼音首字母输入筛选功能。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.pinyin_helper import PinyinHelper


def example_character_filtering():
    """角色筛选示例"""
    print("📋 角色筛选示例\n")
    
    # 模拟角色数据
    class Character:
        def __init__(self, name, cv_name=None):
            self.name = name
            self.cv_name = cv_name
        
        def __repr__(self):
            cv_info = f" (CV: {self.cv_name})" if self.cv_name else ""
            return f"{self.name}{cv_info}"
    
    characters = [
        Character("张三", "张小雨"),
        Character("李明轩", "李娜"),
        Character("王诗涵", "王小明"),
        Character("主角"),
        Character("女主角", "陈奕迅"),
        Character("配角A"),
        Character("小明", "小红"),
        Character("赵钱孙"),
    ]
    
    print("原始角色列表:")
    for char in characters:
        print(f"  - {char}")
    
    # 示例1: 筛选包含"z"的角色
    print(f"\n🔍 筛选包含 'z' 的角色:")
    filtered = PinyinHelper.filter_by_pinyin_input(
        characters, 
        "z", 
        key_func=lambda char: char.name
    )
    for char in filtered:
        print(f"  - {char}")
    
    # 示例2: 精确匹配"zs"
    print(f"\n🔍 精确匹配 'zs' 的角色:")
    filtered = PinyinHelper.filter_by_pinyin_input(
        characters, 
        "zs", 
        key_func=lambda char: char.name
    )
    for char in filtered:
        print(f"  - {char}")
    
    # 示例3: 筛选"lm"开头的角色
    print(f"\n🔍 筛选包含 'lm' 的角色:")
    filtered = PinyinHelper.filter_by_pinyin_input(
        characters, 
        "lm", 
        key_func=lambda char: char.name
    )
    for char in filtered:
        print(f"  - {char}")


def example_pinyin_extraction():
    """拼音提取示例"""
    print("\n\n🔤 拼音首字母提取示例\n")
    
    test_names = [
        "张三",
        "李明轩", 
        "王诗涵",
        "主角",
        "女主角",
        "配角A",
        "小明ABC",
        "张三-1号",
        "李明(主角)",
    ]
    
    print("角色名 → 拼音首字母:")
    for name in test_names:
        pinyin = PinyinHelper.get_text_pinyin_letters(name)
        print(f"  {name:12} → {pinyin}")


def example_gui_integration():
    """GUI集成示例"""
    print("\n\n🖥️ GUI集成使用示例\n")
    
    print("在主窗口中的使用方法:")
    print("""
# 在 main_window.py 中

def on_character_pinyin_input_changed(self):
    '''拼音输入框内容变化处理'''
    input_text = self.character_pinyin_input.text().strip()
    
    if not input_text:
        # 输入为空时显示全部
        self.characters = self.all_characters.copy()
    else:
        # 根据输入的拼音首字母筛选
        self.characters = PinyinHelper.filter_by_pinyin_input(
            self.all_characters,
            input_text,
            key_func=lambda char: char.name
        )
    
    self.update_character_table()
    self.update_status_message(input_text)

def update_status_message(self, input_text):
    '''更新状态栏消息'''
    total = len(self.all_characters)
    filtered = len(self.characters)
    
    if not input_text:
        message = f"显示全部 {filtered} 个角色"
    elif filtered == 0:
        message = f"没有找到拼音首字母包含 '{input_text.upper()}' 的角色"
    else:
        message = f"显示拼音首字母包含 '{input_text.upper()}' 的角色: {filtered}/{total}"
    
    self.status_bar.showMessage(message)
""")


def example_advanced_usage():
    """高级用法示例"""
    print("\n\n🚀 高级用法示例\n")
    
    # 自定义筛选函数
    def custom_filter_function(characters, query):
        """自定义筛选函数，支持多种匹配模式"""
        if not query:
            return characters
        
        query = query.lower().strip()
        results = []
        
        for char in characters:
            # 获取角色名拼音
            name_pinyin = PinyinHelper.get_text_pinyin_letters(char.name).lower()
            
            # 支持多种匹配模式
            if (query in name_pinyin or                    # 部分匹配
                name_pinyin.startswith(query) or           # 开头匹配  
                query in char.name.lower()):               # 直接名称匹配
                results.append(char)
        
        return results
    
    # 测试数据
    class Character:
        def __init__(self, name):
            self.name = name
        def __repr__(self):
            return self.name
    
    characters = [
        Character("张三"),
        Character("张小雨"), 
        Character("李明"),
        Character("李明轩"),
        Character("王诗涵"),
        Character("主角"),
        Character("配角A"),
    ]
    
    # 测试自定义筛选
    test_queries = ["z", "zs", "li", "主"]
    
    print("自定义筛选函数测试:")
    for query in test_queries:
        result = custom_filter_function(characters, query)
        print(f"  查询 '{query}': {result}")


def main():
    """主函数"""
    print("🎯 拼音筛选功能使用示例\n")
    print("="*50)
    
    try:
        example_character_filtering()
        example_pinyin_extraction()
        example_gui_integration()
        example_advanced_usage()
        
        print("\n" + "="*50)
        print("✅ 所有示例运行完成！")
        print("\n💡 提示:")
        print("  - 在实际应用中，可以根据需要调整筛选逻辑")
        print("  - 支持自定义key_func来处理复杂数据结构")
        print("  - 可以结合其他筛选条件实现复合筛选")
        
    except Exception as e:
        print(f"❌ 示例运行出错: {e}")


if __name__ == "__main__":
    main()
