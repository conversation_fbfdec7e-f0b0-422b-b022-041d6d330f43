#!/usr/bin/env python3
"""CV搜索功能演示

此脚本演示CV下拉框的拼音搜索功能。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.pinyin_helper import <PERSON><PERSON>inHelper


def demo_cv_search_logic():
    """演示CV搜索逻辑"""
    print("🎯 CV拼音搜索功能演示\n")
    
    # 模拟CV数据
    cvs = [
        {"name": "张三", "id": "cv_001"},
        {"name": "李明轩", "id": "cv_002"},
        {"name": "王诗涵", "id": "cv_003"},
        {"name": "陈奕迅", "id": "cv_004"},
        {"name": "小红", "id": "cv_005"},
        {"name": "赵钱孙", "id": "cv_006"},
        {"name": "周杰伦", "id": "cv_007"},
        {"name": "刘德华", "id": "cv_008"},
        {"name": "郭富城", "id": "cv_009"},
        {"name": "张学友", "id": "cv_010"},
        {"name": "黎明", "id": "cv_011"},
        {"name": "梁朝伟", "id": "cv_012"},
        {"name": "张曼玉", "id": "cv_013"},
        {"name": "林青霞", "id": "cv_014"},
        {"name": "王菲", "id": "cv_015"},
    ]
    
    print("📋 CV列表及拼音映射:")
    for i, cv in enumerate(cvs, 1):
        pinyin = PinyinHelper.get_text_pinyin_letters(cv["name"])
        print(f"  {i:2d}. {cv['name']:8} → {pinyin:8} (ID: {cv['id']})")
    
    print(f"\n{'='*60}")
    print("🔍 搜索功能演示:")
    
    # 测试不同的搜索输入
    test_queries = [
        ("z", "搜索包含字母Z的CV"),
        ("zs", "搜索拼音为ZS的CV (张三)"),
        ("lm", "搜索拼音包含LM的CV (李明轩)"),
        ("ws", "搜索拼音包含WS的CV (王诗涵)"),
        ("cy", "搜索拼音为CY的CV (陈奕迅)"),
        ("zql", "搜索拼音为ZQL的CV (周杰伦)"),
        ("ldh", "搜索拼音为LDH的CV (刘德华)"),
        ("gfc", "搜索拼音为GFC的CV (郭富城)"),
        ("zxy", "搜索拼音为ZXY的CV (张学友)"),
        ("lm2", "搜索拼音为LM的CV (黎明)"),
        ("lcw", "搜索拼音为LCW的CV (梁朝伟)"),
        ("zmy", "搜索拼音为ZMY的CV (张曼玉)"),
        ("lqx", "搜索拼音为LQX的CV (林青霞)"),
        ("wf", "搜索拼音为WF的CV (王菲)"),
        ("abc", "搜索不存在的拼音组合"),
    ]
    
    for query, description in test_queries:
        print(f"\n📝 {description}")
        print(f"   输入: '{query}'")
        
        # 执行搜索
        matches = []
        for cv in cvs:
            cv_name = cv["name"]
            cv_pinyin = PinyinHelper.get_text_pinyin_letters(cv_name)
            query_upper = query.upper()
            
            # 多种匹配方式
            if (query_upper in cv_pinyin or                    # 拼音包含匹配
                query_upper in cv_name.upper() or              # 直接文本匹配
                cv_pinyin.startswith(query_upper)):             # 拼音开头匹配
                matches.append(f"{cv_name}({cv_pinyin})")
        
        if matches:
            print(f"   结果: {', '.join(matches)} (共{len(matches)}个)")
        else:
            print(f"   结果: 无匹配")


def demo_search_features():
    """演示搜索功能特性"""
    print(f"\n\n🚀 搜索功能特性演示\n")
    
    # 测试大小写不敏感
    print("📝 大小写不敏感测试:")
    test_cases = ["zs", "ZS", "Zs", "zS"]
    cv_name = "张三"
    cv_pinyin = PinyinHelper.get_text_pinyin_letters(cv_name)
    
    for case in test_cases:
        match = case.upper() in cv_pinyin
        status = "✅ 匹配" if match else "❌ 不匹配"
        print(f"   输入 '{case}' 查找 '{cv_name}({cv_pinyin})' → {status}")
    
    # 测试部分匹配
    print(f"\n📝 部分匹配测试:")
    test_name = "李明轩"
    test_pinyin = PinyinHelper.get_text_pinyin_letters(test_name)
    partial_queries = ["l", "lm", "lmx", "m", "mx", "x"]
    
    print(f"   目标: {test_name}({test_pinyin})")
    for query in partial_queries:
        match = query.upper() in test_pinyin
        status = "✅ 匹配" if match else "❌ 不匹配"
        print(f"   输入 '{query}' → {status}")
    
    # 测试开头匹配
    print(f"\n📝 开头匹配测试:")
    test_names = ["张三", "张学友", "张曼玉"]
    query = "z"
    
    print(f"   查询: '{query}'")
    for name in test_names:
        pinyin = PinyinHelper.get_text_pinyin_letters(name)
        match = pinyin.startswith(query.upper())
        status = "✅ 匹配" if match else "❌ 不匹配"
        print(f"   {name}({pinyin}) → {status}")


def demo_ui_integration():
    """演示UI集成效果"""
    print(f"\n\n🖥️ UI集成效果演示\n")
    
    print("在CV分配工具中的使用效果:")
    print("""
🎯 用户操作流程:
1. 用户点击CV下拉框
2. 开始输入拼音首字母 (如: 'zs')
3. 下拉列表实时筛选显示匹配的CV
4. 用户看到 '张三' 出现在筛选结果中
5. 点击选择或按回车确认选择

💡 搜索提示功能:
- 下拉框旁边显示: "💡 支持拼音搜索，如输入 'zs' 查找 '张三'"
- 用户可以直观了解如何使用搜索功能

🔧 技术实现:
- 使用自定义 SearchableComboBox 组件
- 集成 PinyinHelper 进行拼音转换
- 支持实时筛选和多种匹配模式
- 保持与现有CV筛选器的协调工作

✨ 用户体验:
- 输入即搜索，无需额外按钮
- 支持键盘导航 (上下箭头、回车、Escape)
- 大小写不敏感，容错性强
- 视觉反馈清晰，操作直观
""")


def demo_performance():
    """演示性能表现"""
    print(f"\n\n⚡ 性能表现演示\n")
    
    import time
    
    # 生成大量测试数据
    large_cv_list = []
    base_names = ["张三", "李明", "王诗涵", "陈奕迅", "小红", "赵钱孙", "周杰伦", "刘德华"]
    suffixes = ["", "A", "B", "C", "1号", "2号", "配角", "主角", "路人甲", "路人乙"]
    
    for name in base_names:
        for suffix in suffixes:
            large_cv_list.append(f"{name}{suffix}")
    
    print(f"📊 测试数据集大小: {len(large_cv_list)} 个CV")
    
    # 测试搜索性能
    test_queries = ["z", "l", "w", "zs", "lm", "ws", "cy"]
    
    print(f"\n🔍 搜索性能测试:")
    for query in test_queries:
        start_time = time.time()
        
        matches = []
        for cv_name in large_cv_list:
            cv_pinyin = PinyinHelper.get_text_pinyin_letters(cv_name)
            query_upper = query.upper()
            
            if (query_upper in cv_pinyin or 
                query_upper in cv_name.upper() or 
                cv_pinyin.startswith(query_upper)):
                matches.append(cv_name)
        
        end_time = time.time()
        duration = (end_time - start_time) * 1000  # 转换为毫秒
        
        print(f"   查询 '{query}': 找到 {len(matches)} 个结果, 耗时 {duration:.2f}ms")


def main():
    """主函数"""
    print("🎉 CV下拉框拼音搜索功能演示\n")
    print("="*60)
    
    try:
        demo_cv_search_logic()
        demo_search_features()
        demo_ui_integration()
        demo_performance()
        
        print("\n" + "="*60)
        print("✅ 演示完成！")
        print("\n🎯 功能总结:")
        print("  ✅ 支持拼音首字母搜索")
        print("  ✅ 支持多种匹配模式 (包含、开头、直接)")
        print("  ✅ 大小写不敏感")
        print("  ✅ 实时筛选响应")
        print("  ✅ 高性能处理大数据集")
        print("  ✅ 友好的用户界面")
        print("  ✅ 与现有功能完美集成")
        
        print("\n💡 在CV分配工具中的使用:")
        print("  1. 启动CV分配工具")
        print("  2. 选择书籍并加载角色和CV数据")
        print("  3. 在CV下拉框中输入拼音首字母进行搜索")
        print("  4. 享受高效的CV查找体验！")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
