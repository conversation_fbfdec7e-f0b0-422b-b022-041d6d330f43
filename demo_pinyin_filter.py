#!/usr/bin/env python3
"""拼音筛选功能演示

此脚本演示新增的拼音首字母输入筛选功能的使用方法。
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.pinyin_helper import PinyinHelper


def demo_basic_usage():
    """演示基本用法"""
    print("🎯 拼音筛选功能基本用法演示\n")
    
    # 模拟角色数据
    characters = [
        "张三", "张小雨", "张伟", "张敏",
        "李明", "李明轩", "李小花", "李娜",
        "王诗涵", "王小明", "王大力",
        "主角", "女主角", "男主角",
        "配角A", "配角B", "路人甲",
        "赵钱孙", "周杰伦", "陈奕迅",
        "小明", "小红", "小刚"
    ]
    
    print(f"📋 角色列表 (共{len(characters)}个):")
    for i, char in enumerate(characters, 1):
        pinyin_letters = PinyinHelper.get_text_pinyin_letters(char)
        print(f"  {i:2d}. {char} ({pinyin_letters})")
    
    print("\n" + "="*50)
    
    # 演示不同的筛选输入
    test_inputs = [
        ("z", "筛选包含字母Z的角色"),
        ("zs", "筛选拼音为ZS的角色 (张三)"),
        ("lm", "筛选拼音包含LM的角色 (李明相关)"),
        ("wx", "筛选拼音包含WX的角色 (王小明)"),
        ("pj", "筛选拼音包含PJ的角色 (配角)"),
        ("xm", "筛选拼音为XM的角色 (小明)"),
        ("zj", "筛选拼音包含ZJ的角色 (主角相关)"),
    ]
    
    for input_text, description in test_inputs:
        print(f"\n🔍 {description}")
        print(f"   输入: '{input_text}'")
        
        filtered = PinyinHelper.filter_by_pinyin_input(
            characters, 
            input_text
        )
        
        if filtered:
            print(f"   结果: {filtered} (共{len(filtered)}个)")
        else:
            print(f"   结果: 无匹配结果")


def demo_advanced_features():
    """演示高级功能"""
    print("\n\n🚀 拼音筛选高级功能演示\n")
    
    # 测试大小写不敏感
    print("📝 大小写不敏感测试:")
    test_cases = ["zs", "ZS", "Zs", "zS"]
    characters = ["张三", "李明", "王诗涵"]
    
    for case in test_cases:
        result = PinyinHelper.filter_by_pinyin_input(characters, case)
        print(f"   输入 '{case}' → {result}")
    
    # 测试特殊字符处理
    print("\n🔧 特殊字符处理测试:")
    special_chars = [
        "张三-配角",
        "李明(主角)",
        "王诗涵 123",
        "小明ABC",
        "配角A-1号"
    ]
    
    for char in special_chars:
        pinyin = PinyinHelper.get_text_pinyin_letters(char)
        print(f"   '{char}' → '{pinyin}'")
    
    # 测试部分匹配
    print("\n🎯 部分匹配测试:")
    characters = ["张小雨", "李明轩", "王诗涵", "赵钱孙李"]
    test_inputs = ["z", "l", "w", "zq", "sl"]
    
    for input_text in test_inputs:
        result = PinyinHelper.filter_by_pinyin_input(characters, input_text)
        print(f"   输入 '{input_text}' → {result}")


def demo_performance():
    """演示性能"""
    print("\n\n⚡ 性能演示\n")
    
    import time
    
    # 生成大量测试数据
    large_dataset = []
    base_names = ["张三", "李明", "王诗涵", "赵钱孙", "周杰伦", "陈奕迅", "刘德华", "郭富城"]
    suffixes = ["", "A", "B", "C", "1号", "2号", "配角", "主角", "路人"]
    
    for name in base_names:
        for suffix in suffixes:
            large_dataset.append(f"{name}{suffix}")
    
    print(f"📊 测试数据集大小: {len(large_dataset)} 个角色")
    
    # 测试筛选性能
    test_queries = ["z", "l", "w", "zs", "lm", "ws"]
    
    for query in test_queries:
        start_time = time.time()
        result = PinyinHelper.filter_by_pinyin_input(large_dataset, query)
        end_time = time.time()
        
        duration = (end_time - start_time) * 1000  # 转换为毫秒
        print(f"   查询 '{query}': 找到 {len(result)} 个结果, 耗时 {duration:.2f}ms")


def main():
    """主函数"""
    print("🎉 欢迎使用拼音筛选功能演示程序！\n")
    
    # 检查pypinyin是否可用
    if not PinyinHelper.__dict__.get('PYPINYIN_AVAILABLE', False):
        print("⚠️ 警告: pypinyin库不可用，将使用内置字典")
        print("   建议运行: pip install pypinyin\n")
    else:
        print("✅ pypinyin库已加载，使用高精度拼音转换\n")
    
    try:
        # 运行演示
        demo_basic_usage()
        demo_advanced_features()
        demo_performance()
        
        print("\n" + "="*60)
        print("🎯 演示完成！")
        print("\n💡 在CV分配工具中的使用方法:")
        print("   1. 在角色列表上方找到 '输入拼音首字母' 输入框")
        print("   2. 输入拼音首字母进行实时筛选 (如: zs, lm, wx)")
        print("   3. 输入不区分大小写，支持部分匹配")
        print("   4. 清空输入框或点击'清空'按钮显示所有角色")
        print("   5. 也可以继续使用A-Z按钮进行单字母筛选")
        
    except KeyboardInterrupt:
        print("\n\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")


if __name__ == "__main__":
    main()
